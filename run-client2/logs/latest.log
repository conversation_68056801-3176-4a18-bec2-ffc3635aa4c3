[15:16:04] [main/INFO] (FabricLoader/GameProvider) Loading Minecraft 1.20.1 with Fabric Loader 0.16.13
[15:16:04] [main/INFO] (FabricLoader) Loading 59 mods:
	- fabric-api 0.92.5+1.20.1
	- fabric-api-base 0.4.32+1802ada577
	- fabric-api-lookup-api-v1 1.6.37+1802ada577
	- fabric-biome-api-v1 13.0.14+1802ada577
	- fabric-block-api-v1 1.0.12+1802ada577
	- fabric-block-view-api-v2 1.0.3+924f046a77
	- fabric-blockrenderlayer-v1 1.1.42+1802ada577
	- fabric-client-tags-api-v1 1.1.3+1802ada577
	- fabric-command-api-v1 1.2.35+f71b366f77
	- fabric-command-api-v2 2.2.14+1802ada577
	- fabric-commands-v0 0.2.52+df3654b377
	- fabric-containers-v0 0.1.66+df3654b377
	- fabric-content-registries-v0 4.0.13+1802ada577
	- fabric-convention-tags-v1 1.5.6+1802ada577
	- fabric-crash-report-info-v1 0.2.20+1802ada577
	- fabric-data-attachment-api-v1 1.0.2+de0fd6d177
	- fabric-data-generation-api-v1 12.3.6+1802ada577
	- fabric-dimensions-v1 2.1.55+1802ada577
	- fabric-entity-events-v1 1.6.1+1c78457f77
	- fabric-events-interaction-v0 0.6.4+13a40c6677
	- fabric-events-lifecycle-v0 0.2.64+df3654b377
	- fabric-game-rule-api-v1 1.0.41+1802ada577
	- fabric-gametest-api-v1 1.2.15+1802ada577
	- fabric-item-api-v1 2.1.29+1802ada577
	- fabric-item-group-api-v1 4.0.14+1802ada577
	- fabric-key-binding-api-v1 1.0.38+1802ada577
	- fabric-keybindings-v0 0.2.36+df3654b377
	- fabric-lifecycle-events-v1 2.2.23+1802ada577
	- fabric-loot-api-v2 1.2.3+1802ada577
	- fabric-loot-tables-v1 1.1.47+9e7660c677
	- fabric-message-api-v1 5.1.10+1802ada577
	- fabric-mining-level-api-v1 2.1.52+1802ada577
	- fabric-model-loading-api-v1 1.0.4+1802ada577
	- fabric-models-v0 0.4.3+9386d8a777
	- fabric-networking-api-v1 1.3.13+13a40c6677
	- fabric-networking-v0 0.3.53+df3654b377
	- fabric-object-builder-api-v1 11.1.5+e35120df77
	- fabric-particles-v1 1.1.3+1802ada577
	- fabric-recipe-api-v1 1.0.23+1802ada577
	- fabric-registry-sync-v0 2.3.5+1802ada577
	- fabric-renderer-api-v1 3.2.2+1802ada577
	- fabric-renderer-indigo 1.5.3+85287f9f77
	- fabric-renderer-registries-v1 3.2.47+df3654b377
	- fabric-rendering-data-attachment-v1 0.3.39+92a0d36777
	- fabric-rendering-fluids-v1 3.0.29+1802ada577
	- fabric-rendering-v0 1.1.50+df3654b377
	- fabric-rendering-v1 3.0.9+1802ada577
	- fabric-resource-conditions-api-v1 2.3.9+1802ada577
	- fabric-resource-loader-v0 0.11.12+fb82e9d777
	- fabric-screen-api-v1 2.0.9+1802ada577
	- fabric-screen-handler-api-v1 1.3.32+1802ada577
	- fabric-sound-api-v1 1.0.14+1802ada577
	- fabric-transfer-api-v1 3.3.6+8dd72ea377
	- fabric-transitive-access-wideners-v1 4.3.2+1802ada577
	- fabricloader 0.16.13
	- java 17
	- minecraft 1.20.1
	- mixinextras 0.4.1
	- pokecobbleclaim 1.0.0
[15:16:04] [main/INFO] (FabricLoader/Mixin) SpongePowered MIXIN Subsystem Version=0.8.7 Source=file:/home/<USER>/.gradle/caches/modules-2/files-2.1/net.fabricmc/sponge-mixin/0.15.4+mixin.0.8.7/6a12aacc794f1078458433116e9ed42c1cc98096/sponge-mixin-0.15.4+mixin.0.8.7.jar Service=Knot/Fabric Env=CLIENT
[15:16:05] [main/INFO] (FabricLoader/Mixin) Loaded Fabric development mappings for mixin remapper!
[15:16:05] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_16
[15:16:05] [main/INFO] (FabricLoader/Mixin) Compatibility level set to JAVA_17
[15:16:06] [main/INFO] (FabricLoader/MixinExtras|Service) Initializing MixinExtras via com.llamalad7.mixinextras.service.MixinExtrasServiceImpl(version=0.4.1).
[15:16:12] [Datafixer Bootstrap/INFO] (Minecraft) 188 Datafixer optimizations took 287 milliseconds
[15:16:15] [Render thread/INFO] (Minecraft) Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[15:16:16] [Render thread/ERROR] (Minecraft) Failed to verify authentication
com.mojang.authlib.exceptions.InvalidCredentialsException: Status: 401
	at com.mojang.authlib.exceptions.MinecraftClientHttpException.toAuthenticationException(MinecraftClientHttpException.java:56) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:156) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.<init>(YggdrasilUserApiService.java:55) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService.createUserApiService(YggdrasilAuthenticationService.java:124) ~[authlib-4.0.43.jar:?]
	at net.minecraft.client.MinecraftClient.createUserApiService(MinecraftClient.java:733) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.MinecraftClient.<init>(MinecraftClient.java:442) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.minecraft.client.main.Main.main(Main.java:211) ~[minecraft-merged-7787b014d4-1.20.1-net.fabricmc.yarn.1_20_1.1.20.1+build.10-v2.jar:?]
	at net.fabricmc.loader.impl.game.minecraft.MinecraftGameProvider.launch(MinecraftGameProvider.java:480) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.Knot.launch(Knot.java:74) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.loader.impl.launch.knot.KnotClient.main(KnotClient.java:23) ~[fabric-loader-0.16.13.jar:?]
	at net.fabricmc.devlaunchinjector.Main.main(Main.java:86) ~[dev-launch-injector-0.2.1+build.8.jar:?]
Caused by: com.mojang.authlib.exceptions.MinecraftClientHttpException: Status: 401
	at com.mojang.authlib.minecraft.client.MinecraftClient.readInputStream(MinecraftClient.java:85) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.minecraft.client.MinecraftClient.get(MinecraftClient.java:48) ~[authlib-4.0.43.jar:?]
	at com.mojang.authlib.yggdrasil.YggdrasilUserApiService.fetchProperties(YggdrasilUserApiService.java:129) ~[authlib-4.0.43.jar:?]
	... 9 more
[15:16:16] [Render thread/INFO] (Minecraft) Setting user: Player2
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim mod
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered client town jobs network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Reset all town and player data versions
[15:16:16] [Render thread/INFO] (pokecobbleclaim) No individual town files found
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Town data loading completed. Final town count in TownManager: 0
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection handler initialized
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Simple chunk protection system initialized successfully - complex systems disabled
[15:16:16] [Render thread/INFO] (pokecobbleclaim) === Testing Simple Permission System ===
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ✓ Test 1: Unclaimed chunk logic verified (allows all actions)
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ✓ Test 2: Permission constants are correct
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ✓ Test 3: Permission name mapping works correctly
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 0
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [true, false, false, false, false, false, false, false]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 0
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 0
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ✓ Test 4: Individual rank permissions work correctly (non-hierarchical)
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 1
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = true
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank null (null), permissionIndex 0
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Non-member permission result = false
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ✓ Test 5: Non-member permissions work correctly
[15:16:16] [Render thread/INFO] (pokecobbleclaim) === All Simple Permission System Tests PASSED ===
[15:16:16] [Render thread/INFO] (pokecobbleclaim) === Testing Individual Rank Permissions (Non-Hierarchical) ===
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MEMBER (Resident), permissionIndex 2
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MEMBER: [false, false, true, false, false, false, false, false]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank VISITOR (Citizen), permissionIndex 2
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank VISITOR: [false, false, false, false, false, false, false, false]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank MODERATOR (Council), permissionIndex 2
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank MODERATOR: [false, false, false, false, false, false, false, false]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = false
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Checking permission for rank OWNER (Mayor), permissionIndex 2
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Retrieved permissions array for rank OWNER: [true, true, true, true, true, true, true, true]
[15:16:16] [Render thread/INFO] (pokecobbleclaim) RANK PERMISSIONS DEBUG: Town member permission result = true
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ✓ Individual rank permissions test PASSED - only specified rank gets permission (plus admin ranks)
[15:16:16] [Render thread/INFO] (pokecobbleclaim) 🎉 ALL PERMISSION SYSTEM TESTS PASSED! The new simple system is working correctly.
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Claim tag sync handlers initialized
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Starting initialization...
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Registering server-side handler for CHUNK_CLAIM_REQUEST: pokecobbleclaim:chunk_claim_request
[15:16:16] [Render thread/INFO] (pokecobbleclaim) ChunkClaimPacketHandler: Successfully registered CHUNK_CLAIM_REQUEST handler
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Chunk claim packet handlers initialized
[15:16:16] [Render thread/INFO] (pokecobbleclaim) Chunk claim sync handlers initialized
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Claim tool selection sync handlers initialized
[15:16:17] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim mod initialized successfully
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing PokeCobbleClaim client
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering town keybinding
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering chunk boundary renderer
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Loaded user preferences from: pokecobbleclaim-user-preferences.json
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Client Permission Handler v2 temporarily disabled for debugging
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initialized client-side data managers for synchronization
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering sounds
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:notification.invite
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Successfully registered sound: pokecobbleclaim:ui.button.click
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered sounds on client side
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering client-side network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered ClientGlobalChunkSyncHandler network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (client-side)
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer client-side packet handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_SYNC handler: pokecobbleclaim:config_sync
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered USER_PREFERENCES_SYNC handler: pokecobbleclaim:user_preferences_sync
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering server-side network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered town jobs network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered town request network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered GlobalChunkSyncHandler network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Enhanced Image Transfer Manager initialized (server-side)
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering ConfigSynchronizer server-side packet handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_REQUEST handler: pokecobbleclaim:config_request
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered CONFIG_UPDATE handler: pokecobbleclaim:config_update
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing default client products
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Economy client-side network handlers registered
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered client-side network handlers
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing phone feature
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing app position manager
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Setting up default app positions
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initialized 24 default app positions
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing app registry
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered 6 apps
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing phone texture manager
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initializing and registering phone notification overlay
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering phone notification overlay renderer
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering phone notification renderer
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered phone notification renderer
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering phone keybinding
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Initialized phone feature
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registering Shape Visualizer Tool
[15:16:17] [Render thread/INFO] (pokecobbleclaim) Registered shape visualizer tool
[15:16:17] [Render thread/INFO] (pokecobbleclaim) PokeCobbleClaim client initialized successfully
[15:16:17] [Render thread/INFO] (Indigo) [Indigo] Registering Indigo renderer!
[15:16:17] [Render thread/INFO] (Minecraft) Backend library: LWJGL version 3.3.1 SNAPSHOT
[15:16:18] [Render thread/INFO] (Minecraft) Reloading ResourceManager: vanilla, fabric (fabric-resource-loader-v0, fabric-screen-api-v1, fabric-block-view-api-v2, fabric-model-loading-api-v1, fabric-crash-report-info-v1, fabric-command-api-v1, fabric-models-v0, fabric-networking-v0, fabric-biome-api-v1, fabric-api-base, fabric-api, fabric-convention-tags-v1, fabric-registry-sync-v0, fabric-events-lifecycle-v0, fabric-rendering-fluids-v1, fabric-lifecycle-events-v1, fabric-command-api-v2, fabric-renderer-api-v1, fabric-particles-v1, fabric-item-api-v1, fabric-commands-v0, fabric-resource-conditions-api-v1, fabricloader, fabric-client-tags-api-v1, fabric-mining-level-api-v1, pokecobbleclaim, fabric-block-api-v1, fabric-api-lookup-api-v1, fabric-networking-api-v1, fabric-renderer-registries-v1, fabric-recipe-api-v1, fabric-gametest-api-v1, fabric-content-registries-v0, fabric-renderer-indigo, fabric-data-attachment-api-v1, fabric-dimensions-v1, fabric-loot-api-v2, fabric-transitive-access-wideners-v1, fabric-item-group-api-v1, fabric-transfer-api-v1, fabric-sound-api-v1, fabric-loot-tables-v1, fabric-keybindings-v0, fabric-message-api-v1, fabric-containers-v0, fabric-game-rule-api-v1, fabric-rendering-v0, fabric-object-builder-api-v1, fabric-rendering-v1, fabric-rendering-data-attachment-v1, fabric-screen-handler-api-v1, fabric-events-interaction-v0, fabric-entity-events-v1, fabric-blockrenderlayer-v1, fabric-data-generation-api-v1, fabric-key-binding-api-v1)
[15:16:19] [Worker-Main-9/INFO] (Minecraft) Found unifont_all_no_pua-15.0.06.hex, loading
[15:16:19] [Realms Notification Availability checker #1/INFO] (Minecraft) Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: FabricMC
[15:16:21] [Render thread/WARN] (Minecraft) Missing sound for event: minecraft:item.goat_horn.play
[15:16:21] [Render thread/WARN] (Minecraft) Missing sound for event: minecraft:entity.goat.screaming.horn_break
[15:16:21] [Render thread/INFO] (Minecraft) OpenAL initialized on device Sound Blaster GC7 Analog Stereo
[15:16:21] [Render thread/INFO] (Minecraft) Sound engine started
[15:16:21] [Render thread/INFO] (Minecraft) Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[15:16:21] [Render thread/INFO] (Minecraft) Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[15:16:22] [Render thread/WARN] (Minecraft) Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[15:16:22] [Render thread/INFO] (Minecraft) Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[15:16:22] [Render thread/INFO] (Minecraft) Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[15:16:22] [Render thread/INFO] (Minecraft) Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[15:16:29] [Render thread/INFO] (Minecraft) Connecting to localhost, 25565
[15:16:29] [Server Connector #1/ERROR] (Minecraft) Couldn't connect to server
io.netty.channel.AbstractChannel$AnnotatedConnectException: finishConnect(..) failed: Connection refused: localhost/127.0.0.1:25565
Caused by: java.net.ConnectException: finishConnect(..) failed: Connection refused
	at io.netty.channel.unix.Errors.newConnectException0(Errors.java:155) ~[netty-transport-native-unix-common-4.1.82.Final.jar:?]
	at io.netty.channel.unix.Errors.handleConnectErrno(Errors.java:128) ~[netty-transport-native-unix-common-4.1.82.Final.jar:?]
	at io.netty.channel.unix.Socket.finishConnect(Socket.java:359) ~[netty-transport-native-unix-common-4.1.82.Final.jar:?]
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.doFinishConnect(AbstractEpollChannel.java:710) ~[netty-transport-classes-epoll-4.1.82.Final.jar:?]
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.finishConnect(AbstractEpollChannel.java:687) ~[netty-transport-classes-epoll-4.1.82.Final.jar:?]
	at io.netty.channel.epoll.AbstractEpollChannel$AbstractEpollUnsafe.epollOutReady(AbstractEpollChannel.java:567) ~[netty-transport-classes-epoll-4.1.82.Final.jar:?]
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:489) ~[netty-transport-classes-epoll-4.1.82.Final.jar:?]
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:397) ~[netty-transport-classes-epoll-4.1.82.Final.jar:?]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.82.Final.jar:?]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.82.Final.jar:?]
	at java.lang.Thread.run(Thread.java:840) ~[?:?]
[15:16:43] [Render thread/INFO] (Minecraft) Connecting to localhost, 25565
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: handleTownListResponse called - packet received!
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town list update with 213 towns
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwa (ID: bc555394-bbf0-475a-95a1-0b17c9d36c0f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwadawa (ID: 853b9a47-40bf-4521-b523-868339a8a0ee)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadw (ID: 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawdaw (ID: 6a314cd9-189f-4b6e-864d-81aa21f6af0f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwad (ID: 6c57a9a0-6448-452d-a814-b8d9040e6043)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadw (ID: 20ede486-11b9-4d1f-b8cc-202ccc0baa34)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdssadasd (ID: d72feaa8-f4c1-4532-a294-8e00634a0dbf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwaaw (ID: e91a1bc1-2579-49dc-802b-cf19d448d911)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadwadaw (ID: 906f24a7-d1ca-455c-aca0-dbc94dbed0a8)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a a a a aa a  a a a a a aa (ID: 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adawdawd (ID: dcf00911-67f1-4159-9367-6c4c81c89f88)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaadasdasdad (ID: 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdrewrghr (ID: 6e99a3dd-33fe-4563-981f-05d4583d920a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadw (ID: a39c6ace-b46a-47d1-bd08-5fbfc078aa17)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adjsa (ID: be0d82f4-3880-4b9e-a3eb-5e747ebaeef3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadwa (ID: 669d5f05-6ef9-4e7f-a17b-ca50fc162411)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawda (ID: 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawd (ID: d9786d4d-fc23-4454-af9f-f9adafa9bdec)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waaaaaaaaaaaaptssssssssssss (ID: 464f7431-c572-4387-8f91-4edb6613f7e4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawd (ID: 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwaa (ID: f10db762-14e2-4e7e-aa38-41b601cda088)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdwadwa (ID: 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ougaga (ID: 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testicule (ID: b0d2f8dd-3094-42ab-bee1-07f2579bb4ff)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwdada (ID: 9a5193c2-4fea-49c7-a4d6-d8709e7de329)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadadwdawd (ID: 9c1b8f4a-051c-4a69-9c92-5ac16b610544)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadasda (ID: 17af5a11-bd38-41e4-a574-70779fdb21ee)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwada (ID: 78f54b6b-e425-4d18-9933-469118c7019d)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwa (ID: 9248516c-92ac-4cd0-8cdf-1dc330367029)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessir (ID: f0e54b62-4630-4c36-b388-f8caa0ee9e71)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadaw (ID: 88840423-accd-4f2e-8fca-d372b807cda7)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwawd (ID: 032985e0-551d-422f-9893-e791ea418ce6)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdw (ID: 741e6313-004f-46bb-ba8c-cee7a6b12f43)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdwadawda (ID: 4d60dd17-44c1-4e97-9888-32e563b56199)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanusima (ID: 05e3661f-4790-4fc7-8368-85bc77f1aeef)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - addawdadwad (ID: 733810c2-ee73-4c36-8f01-f32508c7b33f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaa (ID: 6f823d06-f53d-478b-b50e-766154019f39)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasa (ID: 27907c34-f576-4d02-b00d-ae74912e0ce2)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdwad (ID: 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ticsssss (ID: 0250cce5-1df9-4fb2-9cce-04b98f710590)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdad (ID: 08fda338-bbea-426e-acd8-916d2e3d0fa5)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdawd (ID: 0f27042d-3329-4823-9fb7-bbf7455feb6c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawda (ID: 5b2b5170-839e-48e7-9363-c5749cb7cff1)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddwadwadw (ID: dcad3d2a-859d-48a6-96ea-a1e0a9c5840b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawda (ID: 8d5a42ff-95fd-48a8-b128-5d9776ebe6de)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - safwef (ID: a68bf3e5-5d2f-436d-8d5a-0410d5c285d9)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadaw (ID: 024247bf-9275-4b17-9615-4ee89f4529a3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test1000 (ID: 1c41e035-1e9c-4508-9095-8b85fd99b3ed)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - alo (ID: 9bd7ba35-3161-4175-b0a1-bab05aff119c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadasdadasd (ID: 853f9480-d66b-4dfa-985e-2a4d71339b88)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdadw (ID: 5abfa938-5fc3-4d16-b0da-fbbda5929320)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaaa (ID: 50c8de1a-75e0-49b1-b98a-b1eb195a7331)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadaws (ID: a829eda3-08b2-4d0d-a59d-e94f2c590635)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawd (ID: 77198cd6-f6e9-47d5-9195-b53da486c253)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwawa (ID: e8f21959-b887-4d17-b8bc-c21ca9c67bd2)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaa (ID: e39ae71e-3f67-4511-9f30-5ecca1ab9a34)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadwada (ID: 24de29f1-549b-46a3-81aa-1e50e914fb6c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaw (ID: b883ad5c-d027-4583-bd31-cf09b80448de)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a s s sd d  d d d d d d d d d (ID: 04d2ca7a-2895-4135-9fe9-bed8bf252b82)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwa (ID: 3b7f071f-4ae7-4fb1-a420-9df4a30a0577)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdawd (ID: c05f7e64-51ec-4c7b-9853-5c0792891dd0)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadasdaw (ID: 61e12bd6-3ac2-4033-894d-78869890173f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - DWDAWA (ID: 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwadwad (ID: 483238da-c158-4139-b084-9d533f5718ac)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdaw (ID: feaef9f1-3244-4dec-acaf-22899d837df7)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawwad (ID: e8bd1386-9e2c-4865-b9e2-3a58d5e22f82)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaa (ID: 8259edfd-51df-4a27-b0d6-47aa85fb2073)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwdwa (ID: cabe8ac7-596d-491c-91de-3b4ce8a66892)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pooeooooooooooooooo (ID: 70481211-c3b9-44a0-ba28-6940bcb26a34)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadaw (ID: 8bcf849e-3b9f-45ce-888d-ff3abe156a0c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdawd (ID: 26f52792-3e06-45a4-a84d-39ffa6585381)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: 75c4e2af-d0a7-4da7-9292-0cc6250de180)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwad (ID: 4cd60086-5feb-4f59-aa1a-d1989fc01c14)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - tee (ID: 18a45882-f713-4b73-9217-94a805ac35e0)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadw (ID: ce1eec03-90dd-4e55-aeb9-e402e83f88a4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawa (ID: 100982b7-1713-47f8-b1b7-e9e66cdf706d)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wDwdadww (ID: fa316ed1-964f-4935-977e-9902843af565)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwa (ID: c551b58b-33cc-47f2-b6b0-2dcf154b906e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdawdawdaw (ID: 398b6c99-8cb1-4ed1-8c38-08623d693883)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm (ID: e6568df3-4194-44d1-9846-44afadaebd6e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwawdwa (ID: 5b56da19-9d90-4027-b4d7-37f49dd40ac4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawda (ID: feea5a92-9db6-4071-9c56-053fae2e7c35)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawda (ID: 16ba766f-660b-446c-83ee-88fcaf32a446)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: fd030878-1735-4865-91ce-f39925b5c51e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwa (ID: 45971005-1752-45a4-80da-ae2ddff4aebf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ouggggggggggg (ID: f93c96c3-740c-4116-8e18-57737635fd63)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - jean paul (ID: 037d4124-446d-441d-8523-ac61548b293b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasdweas (ID: 9336d7bc-c90e-4ddd-b298-cbce4a56dd87)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadawda (ID: c029c77a-2c4f-4dcb-bcae-7b79b02671f8)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwaaw (ID: a519cc83-44ed-4853-b0fa-101caa456766)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test3 (ID: 8aad57c3-16ed-4a95-9422-436154898639)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdadaw (ID: aa09191d-39e3-4375-b0b8-be26bce11118)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsfdfsfs (ID: 24c4f956-bc10-4cce-b33e-1c136f58e3df)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdadsa (ID: 1f0132cb-89fb-452b-bdfc-7a3141593568)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yesssssssssss (ID: 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawda (ID: bd55053d-02ed-4802-b2fc-121047bd4722)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasfa (ID: c73d558c-d848-49cd-956a-9196a7df24e3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwadwad (ID: 9b41ee08-47e3-4ec5-b69d-21011c15e31e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadawda (ID: 6415c670-1162-4e39-9be8-5d67e9332478)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwdaaa (ID: 7448d1e9-4f39-4475-adcf-e1478168e7f3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - afgee (ID: 7ca97c57-ea96-4c08-8e42-c6d5c004720c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawwda (ID: ef7bd278-fff3-44f9-8dd5-9f4ec7be1424)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawa (ID: d812caf2-0303-4df8-8e53-ebc00de73364)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssssssssssssssssssssssssssss (ID: ee5fafb3-4693-4286-bf63-5aa778375907)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdaawa (ID: 3ee54270-2c98-441f-8906-5a9238ed9a66)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwa (ID: 40af2c0b-19d3-47d2-a1ce-72eb435e53d3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwaaw (ID: ecdedce2-34d7-43e2-ae6c-397a4f759279)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdaa (ID: 00981050-1af7-4fe0-aa17-a4d83fdc07d8)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssss (ID: 29172492-27b5-4802-884f-21ec817307f6)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawa (ID: 0a6490c2-9ec7-4578-96c4-c05c0e4046d4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwa (ID: a65645a5-9915-46ab-a4e5-4a3eafa7aa26)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanus (ID: d40872af-b1f5-431d-b0f7-580fa07c67dc)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdadwa (ID: e720508e-64b6-4294-8179-d176cf6c39f1)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pinis (ID: e40ab16d-fc22-4a3e-8c88-3f44a25383fa)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwadfsdasd (ID: 975c2700-1680-4cc5-8acc-9af93162426d)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdaw (ID: cc637ba9-3435-4ac1-8e37-aff6a2780fa4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - annus (ID: 272cef11-acb6-4ee0-88f2-3f1f3e3c249c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawda (ID: b53ee7aa-7d5f-4052-af2b-43114c79cef2)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwad (ID: 678bd7c2-b672-4c9f-985d-f978d3584ff2)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwadwadaw (ID: 976bc1ed-8f12-4c33-aac7-6bc69cd19558)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - 00000000000000000000000000000000 (ID: 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: a2f28896-5544-4b5d-b50c-fbd99a2e77b5)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasasayaaaaaaaaaaaaaaaaaaaaa (ID: 264224c1-6867-4502-8b49-7aaa42aef79f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaw (ID: ea59a2f8-3ef2-41d0-be76-e78be54ee805)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessirk (ID: d96582db-2363-4cdd-ad68-fd69840e7139)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdas (ID: ffa7b015-7803-48af-871a-e31eec921abf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawaw (ID: d7c3103f-c296-4e38-8900-c087f6d91d3b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdawdwad (ID: ac10c935-e61e-47c5-8e1c-ec5d3a286daf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwwdwa (ID: f51f5d15-e98a-422e-805d-8785df82bcb4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadw (ID: 4995ef19-314c-4a36-a400-6bf82e748d3a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawd (ID: c3e140c2-8b05-4b14-997f-863ed53c000f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsafasfaf (ID: f247d133-8e78-400d-b326-a382fc76969f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wda (ID: cc60a66a-494b-482f-9eab-410b179ae619)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwad (ID: 13b44232-8023-4bce-bcbc-186ad6f9070b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testtttttttttttttttttttttttttttt (ID: 2f388ab9-8161-4f4e-8ffb-1a65afe5449a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadawdad (ID: 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: bd36e320-c182-46a4-ba60-cc3ba4a0749c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadaw (ID: 1cb5ad85-59b9-4095-825f-1364ef6d291d)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - diuashduihawuihduia (ID: 1306a6ad-3ea4-4106-b4ab-09d645770558)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsafasfs (ID: 0f5b2980-3acc-4024-b5b4-19450a419fdf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 62796a47-945e-4936-9a95-4fa450ed07d6)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasad (ID: 16547467-d690-417e-bce9-4cd58a25c298)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawaad (ID: 0c39f35c-a615-4550-ab9f-e295f8be70cb)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwawdwa (ID: f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdaa (ID: a6007f69-3f95-4ecd-acde-d16d6ad62035)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsadwerwffsdfssssss (ID: af66689a-c04b-4249-a424-a710376856c5)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwa (ID: 031efbcf-d63a-42ae-93f6-595fa590e61b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawaw (ID: f7b38e66-7063-48e5-b35a-48877a960372)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaa (ID: a6dd2c36-80bb-4c61-8ea2-865ea919fabf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daasa (ID: 00000cbc-9402-4ef1-b2e3-ba097c34fdfa)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: 0256ccaa-f6f6-478b-bb23-0ebce2fcb993)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwd (ID: 59be9116-4344-48dc-a300-fd54b50cb51f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwaa (ID: 9b47b464-ef47-4123-8afe-b5da2a019d1b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaa (ID: 9e221a54-f864-4c17-9f6b-1d3b7f8350ad)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwada (ID: e1c23312-3859-40b2-ba18-624bd0d595c3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwawda (ID: 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fDAF (ID: 809bbcd3-c153-461e-a399-1ad841c4260c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdaw (ID: 42e9b17d-07df-4fba-bdd2-be676c80274a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdw (ID: aa2110b5-ddd6-4e2d-b17d-765dd51878c7)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsaaaaaaaaaaaa (ID: 2442bf9f-8632-4327-b68a-78aa57b420cf)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 47668211-a0c8-453a-8658-c35c8acf9cdd)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa33333 (ID: b1ecfb16-e7c9-4919-b2b8-370550252426)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadaw (ID: aceee40b-077b-421b-83ee-36e196d28fc3)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddawdwa (ID: 23aa3f82-2714-4521-8789-be2b00331013)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawda (ID: d84f573a-932a-4af7-9095-719939a07fa4)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdaw (ID: 358a13f3-a1db-4741-8235-71300fd72993)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdada (ID: 75c433cf-7bcb-43eb-8d42-c782aa793f38)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwad (ID: f69a3537-b316-401e-8412-303a459930be)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawadasd (ID: ae0e56ab-942d-43ab-8a30-7c16be4a98fe)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdfdsdfa3343224324 (ID: 21638641-943d-4c4b-a075-f3abcc46797f)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 778bd3d6-bc14-459b-a87a-17869d9e9e04)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadasdasd (ID: 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwad (ID: 2adf158d-d3bf-4038-8e49-f53541fa73e8)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawd (ID: 253734e7-c9f5-4d35-bcd6-302e42fc0ea8)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing (ID: 079df915-52f6-4e8c-b5c3-54d04e586c07)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawdadawd (ID: 732435c6-5e90-41c9-a5a4-207559f9bc51)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadawdawd (ID: 067f1e0a-5eef-42cd-981d-65a1ef04392b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawwda (ID: 3793b743-654c-46d4-8b68-dcadb593ceda)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaawwwwwwwwwwwww (ID: 67641bc2-d6a5-400b-b469-50ff5df29aa9)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwaa (ID: 1b8c6372-517b-4aa5-bf37-e0b10c14897a)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawa (ID: 90939159-fabb-462d-9d92-3105cce322e1)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdw (ID: afa43070-a3ac-4134-841f-efc29f43c218)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sfdsfetdfcfvcbbfghfhfhfdgfgrgf (ID: de8544a5-e176-4038-b699-419018a1530d)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaw (ID: 0e4d4772-3d89-453c-ba71-2426e168ef65)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ddddddddddddddddddddddddddddddd (ID: 01ace668-ce82-42b3-8f7c-c9a3219d4967)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing perm (ID: 25fd387c-35b2-43b1-9df4-95f0149c62c9)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sad (ID: 7331902b-ddbb-498e-b365-71d88ae42ca5)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daw (ID: 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - oui (ID: f0806c7c-7cb3-4f6a-9447-5d782af01932)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadwa (ID: f85bd8b2-1374-424a-89b0-2625bc473bd8)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadw (ID: 4a7dbb6e-8289-4274-9a11-25a61493c128)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawddaw (ID: a5d6a06b-a9c7-4067-be5e-a0209e201fa0)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawd (ID: 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaaaaaa (ID: 08db5308-e82e-4ee5-9f66-1f66a5da672b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsadasdasd (ID: c2569049-fcdf-4278-ac80-d1cf35c645db)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwdadw (ID: a06dce52-acae-4249-bc9b-eb3840ee3e7e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawdawddaw (ID: 9ff3db28-a0d5-45cd-9b59-fd34d9640724)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 8a568eb9-0e15-4e8f-8185-96874fe6477e)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fuk (ID: 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - rrew (ID: 2f9a5448-417b-4f32-8023-44c71886c60c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawddwa (ID: d2fa02b5-eea0-495a-949c-d32c8e171be7)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdadw (ID: 612ff681-0fbe-42d1-b927-d4b9c12a5982)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwaad (ID: 9ee99cde-9bb9-4d9d-83ee-df2342551b7c)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdadwadsa (ID: 116028e9-21cf-435b-bedd-3745d285c7fa)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdaw (ID: 31d13c67-f565-4bd0-9c4d-a9ab620a5913)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwadwad (ID: f67d2e01-39e2-4cb0-8180-7dc6c1d78342)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: b51e7ecf-d238-48e7-8624-9540bf816a14)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dasdawdwa (ID: 2d685456-5807-4590-b4ec-2648a069959b)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdad (ID: 220f69b7-1a72-4180-9533-1cb20000f234)
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town bc555394-bbf0-475a-95a1-0b17c9d36c0f, firing player update events for 2 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3584c7cd-614b-3687-b1ad-b239e7a37bd8 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a288cb29-a681-33d0-bc05-44996f7b6ce1 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 853b9a47-40bf-4521-b523-868339a8a0ee, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b4af1ef6-4402-3592-bf8a-f26295105c46 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e248ad6f-48ae-34b1-a009-03f2794f8f73 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 6a314cd9-189f-4b6e-864d-81aa21f6af0f, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 777409db-46bf-31bf-844f-6d600c083d6c with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 6c57a9a0-6448-452d-a814-b8d9040e6043, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5bd62aa7-8886-32aa-953e-97bdbc2a6931 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 20ede486-11b9-4d1f-b8cc-202ccc0baa34, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1b8221f7-5861-330e-bef0-ac96b785ff27 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d72feaa8-f4c1-4532-a294-8e00634a0dbf, firing player update events for 2 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b4e0083c-c857-3e3a-a915-f92e6611bf03 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e2fd3051-a7ee-3ec9-ae1f-a1b6240b0501 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e91a1bc1-2579-49dc-802b-cf19d448d911, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6b53dfcf-6b4c-38dc-935a-30b749f0d474 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 906f24a7-d1ca-455c-aca0-dbc94dbed0a8, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 533168cf-a4ab-3aac-8306-f7a8b71689a4 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 56a259bf-cc44-38c1-8061-dd05953c60e0 with rank Resident
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town dcf00911-67f1-4159-9367-6c4c81c89f88, firing player update events for 1 players
[15:16:43] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player dfdc5dbd-4a0e-3b6a-a815-601a03741434 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 453f7d61-95c2-3540-9f32-1609f539a60e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 64579c2e-a410-37b0-995d-5aa98a8b0304 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 6e99a3dd-33fe-4563-981f-05d4583d920a, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 34077235-b312-3a6b-9fdd-0d5e06f91c0e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e842b536-bd2e-36a7-9e4d-a97b10ab8389 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a39c6ace-b46a-47d1-bd08-5fbfc078aa17, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 99e80a89-582d-3fb3-8388-49270da696a6 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town be0d82f4-3880-4b9e-a3eb-5e747ebaeef3, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 83a9d3a3-9e1e-31ca-9e08-18626d61aedc with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 669d5f05-6ef9-4e7f-a17b-ca50fc162411, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 02006724-8599-3350-ba13-0c0625a51a5e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 93caad21-f530-38b7-b8b7-9239abf6836d with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d9786d4d-fc23-4454-af9f-f9adafa9bdec, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4ebb02a4-f60f-3bbd-8cd6-a4ac4ed3f510 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 464f7431-c572-4387-8f91-4edb6613f7e4, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6a12f6e4-e9d6-38e2-bb44-10484bb75cf2 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b7b382ee-0706-325d-ba6b-a28fc58baaf1 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 32c6d70c-2b61-390a-8ed9-2ab7ce3ffeef with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f10db762-14e2-4e7e-aa38-41b601cda088, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 63489016-3661-38ec-acb6-3029cde6f29c with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 11cdf046-d362-394c-93e8-583f0c5ef255 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5234a5c3-20f2-3d6d-975b-3ec31fe1a944 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player df8174ec-bca9-3a1b-ba60-043f16a73d79 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town b0d2f8dd-3094-42ab-bee1-07f2579bb4ff, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 63bf8c57-1166-37df-b325-c9439bdf7ed3 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 618cb0ab-8f59-3811-afad-31574b3b487c with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9a5193c2-4fea-49c7-a4d6-d8709e7de329, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ccd8a3d4-50c5-39f5-a01f-10958af9df66 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9c1b8f4a-051c-4a69-9c92-5ac16b610544, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d5e76a54-1748-3850-8a88-7d4b819073e0 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 17af5a11-bd38-41e4-a574-70779fdb21ee, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8ddad301-a7c4-37dc-9ab6-a95e85cbf7dd with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 78f54b6b-e425-4d18-9933-469118c7019d, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 50dde602-6a46-34aa-95e3-8463932e70f7 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9248516c-92ac-4cd0-8cdf-1dc330367029, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ed3ff7cb-6a3b-37aa-85ff-4e536390779c with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b0121b71-1aec-310d-8ee8-525ebe3fec09 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f0e54b62-4630-4c36-b388-f8caa0ee9e71, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1dc55782-3c00-3ed4-bf8b-76d4c6d9cc47 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 88840423-accd-4f2e-8fca-d372b807cda7, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0704e0ba-eaf6-3b26-b880-53fd45ec3b48 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c279a2aa-6379-4f5a-9ec5-bce65fa5d52a, firing player update events for 39 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c97b21a8-25e6-37ce-8a04-8d5bcf6f4c4d with rank Mayor
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fe207da4-45aa-3a2a-bf67-72a700290388 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b2d19156-5364-397f-8e33-abd769d6319d with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2ef1cb8d-614b-3e2e-9366-5d5cf65cf8bf with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0aee4775-e8b4-37ed-8049-6be96ef5de8e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 492dc575-b72e-3d83-b2fd-33ab63727150 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c2506432-c159-30c0-93d8-64f2a6272277 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a0a7ec06-d96a-3187-943b-79bf7e87b3a2 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4941828d-27d9-3ea5-a0e2-8e881702c195 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1ab04e7d-c966-3477-8a64-78d9a4421f72 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2afb4ee9-7113-3f92-8544-46d36df86129 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 99963b1e-252d-3ba6-8126-9071dd762e6c with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7d246c97-157b-31d3-a8fc-0b0f5f437b63 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 19487181-3859-391a-bf75-143cc8396d12 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1dd34f6b-f553-3906-92e2-e13f78ae2b51 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1acd01f1-1c8b-3758-959b-fdf4d16bd5de with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fe412f9e-f508-30d0-b407-ff978cf8fc5c with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 77058a27-9ed9-3577-ba3a-f426b243548e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 905847c0-6737-32d8-85e6-3f64ecd32033 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5f452b45-a444-38e5-8393-5edeaa648813 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 043cbf82-02a9-3180-968e-af73411af080 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 428a9eb8-eb59-3300-892c-1124ca047d38 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3dc994a6-e4ba-3e8a-9bf9-4271ccca9810 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9a9311e6-0e2a-3db9-849c-c74b23116910 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8c6477df-589c-387e-8235-e07170c95726 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e7f0f282-ec78-3bba-aaaf-9edc548f540d with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3959aa12-3a9d-3fc4-84ad-d0d3c7c6550b with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1e878937-6147-358d-b9a5-2d994db008c9 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0d1798c0-e9c5-3a83-99d9-4cf400fcf12a with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d447d002-8f00-3c6c-8a29-93b028d90375 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8dea17cd-e1ef-3064-95ba-e1811660f4f6 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 04aedfc4-153f-3d65-bb99-866996994a71 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1455153c-1276-3617-8489-c22f3316fb17 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 23caebef-63a5-39b8-9ae3-6dfce0636b5f with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b40915cd-d3a7-3e3d-9a52-6fc5334ba2e3 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5c5c7719-d2ce-30c8-bc42-ad9690493df6 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 446b6428-1dda-3773-8986-3a9e0deb6ae4 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f0e019d1-5d03-339b-9d95-1c4fdeb69e95 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 446355e4-985a-3d9c-a63c-4d95aa4b0dff with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 032985e0-551d-422f-9893-e791ea418ce6, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1178851a-8246-318e-85f8-1ae4517330a2 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 741e6313-004f-46bb-ba8c-cee7a6b12f43, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 69715e5e-1775-3a6c-8b64-1d82bcbbe688 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a5319f57-56e8-315e-b8df-e2f59bce41c4 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 4d60dd17-44c1-4e97-9888-32e563b56199, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player de2b0524-f0fe-3ddf-9581-f7fcb562ed9e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 05e3661f-4790-4fc7-8368-85bc77f1aeef, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c72e77a5-a3ff-38a0-b468-253b21a4bd27 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d37346cb-6397-30a1-9ba8-85e1aa0153dd with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 733810c2-ee73-4c36-8f01-f32508c7b33f, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2737bbb4-080a-3670-89c1-780a32c86c5a with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 6f823d06-f53d-478b-b50e-766154019f39, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9ba8005a-2f55-38dd-9454-3d3b1d67bff1 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 27907c34-f576-4d02-b00d-ae74912e0ce2, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6634a609-43e8-3247-9054-c77a3a707d73 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player bfc5c6f8-886e-30ef-aac9-4df3c4277f54 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a9c0d951-c0b6-3353-a779-fe4259ce0834 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0250cce5-1df9-4fb2-9cce-04b98f710590, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e3cbbed2-a904-3501-9aba-c8ad5473501b with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 08fda338-bbea-426e-acd8-916d2e3d0fa5, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 184a029e-ad3d-3abd-8a1a-c044d35081d5 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0f27042d-3329-4823-9fb7-bbf7455feb6c, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b91f0a72-af04-3f0f-855e-a1f30159432b with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 5b2b5170-839e-48e7-9363-c5749cb7cff1, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 171ff2be-e113-3c14-a482-20b6c4712810 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town dcad3d2a-859d-48a6-96ea-a1e0a9c5840b, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7e94e5c9-1ae4-352e-9cf6-8305e5b127c7 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 42468be1-4059-36e2-be82-cb1721e83191 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8d5a42ff-95fd-48a8-b128-5d9776ebe6de, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 05a2b883-672c-35b9-82ec-eccc4e0cabc2 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a68bf3e5-5d2f-436d-8d5a-0410d5c285d9, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9aff406e-3282-3c93-8c5e-62214f4adca0 with rank Resident
[15:16:44] [Render thread/INFO] (pokecobbleclaim) Loading positions for player: aed5efd4-551b-3965-bc28-ae21aa072a66
[15:16:44] [Render thread/INFO] (pokecobbleclaim) Initialized with default positions: 24 entries
[15:16:44] [Render thread/INFO] (pokecobbleclaim) Found 24 saved positions in NBT
[15:16:44] [Render thread/INFO] (pokecobbleclaim) Loaded 25 app positions for player aed5efd4-551b-3965-bc28-ae21aa072a66
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 024247bf-9275-4b17-9615-4ee89f4529a3, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5db36629-721d-3a36-8728-14b3ab112351 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 1c41e035-1e9c-4508-9095-8b85fd99b3ed, firing player update events for 15 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f0737fb5-4d0f-3bee-a039-92e456a5cc2e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 39c4f4aa-4d39-353b-b70e-747427551322 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f9567117-2555-3219-a3d6-01de0ddd7332 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 37e4a5df-2479-34b7-be52-2f491341f6b1 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 76e88f6f-84ed-3a54-af33-5eec444b0454 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2b98548a-5e09-3029-b082-b4c99d31d926 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ab8db6cf-78bd-396a-b9c3-4e2021f3ea40 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0de21b13-73b3-37ba-84f2-92c78b6712c2 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 93bfa0b6-cc14-3c0c-8efa-0bcc48245274 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 39ce36e8-f4b8-3b30-a1f4-bfdbbc990254 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1fa8a2b7-811f-322e-b30d-8bfc6ccd460c with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ca560dd3-ebf9-3fc9-b2e6-088534082c81 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b8c5ee80-8fcd-32b0-a4c2-2c2e7de58468 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6256f6e3-2e58-3651-998e-2564a0b2a631 with rank Resident
[15:16:44] [Render thread/INFO] (pokecobbleclaim) Registering client-side town commands
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 68a3e4ad-dd19-33fa-8035-4934872a4f77 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9bd7ba35-3161-4175-b0a1-bab05aff119c, firing player update events for 10 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6f525ab9-4df4-3875-8280-4904a48f9cd7 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 909ca267-1200-3a4b-b154-ac49c5d6bf14 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 68301082-0c6f-34cc-9a03-f1d8824d1d08 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7a9eca9c-2306-3ccb-a260-5c2ec3a39f00 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7dc80f62-93ab-31a4-97b1-46f4b80402c9 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 71f66ac1-c861-3de2-8cda-9d0f80277f27 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f4642d2b-29f9-34b7-8b90-e6570e856434 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1e0b9d62-e071-33ba-b82e-1c564a46e2f0 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c9700ac2-fb69-36ca-b322-09edfc5786fb with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 33978c47-85f0-3cad-8a2d-42421a4d8510 with rank Resident
[15:16:44] [Render thread/INFO] (pokecobbleclaim) Client-side town commands registered successfully
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 853f9480-d66b-4dfa-985e-2a4d71339b88, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fb99d07b-9b6d-3e33-b8da-b8b7c2f42608 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 5abfa938-5fc3-4d16-b0da-fbbda5929320, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e0b801b1-6b73-363a-a46f-105be4f0f90f with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 50c8de1a-75e0-49b1-b98a-b1eb195a7331, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8f2ecf49-60e3-3c84-b5c7-945f8f24636d with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a829eda3-08b2-4d0d-a59d-e94f2c590635, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1ba3e813-bc3f-3c63-a07a-82ed99656a6d with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 77198cd6-f6e9-47d5-9195-b53da486c253, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2107fded-a4bd-35bb-abc7-dae190b9909f with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e8f21959-b887-4d17-b8bc-c21ca9c67bd2, firing player update events for 2 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d566deca-6b21-32cb-af90-4edf09eeac2e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4a5060c2-513d-3571-8c5d-062b588e69ca with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e39ae71e-3f67-4511-9f30-5ecca1ab9a34, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player eb7081a5-32ec-3752-9cbc-963a701e6fdd with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 24de29f1-549b-46a3-81aa-1e50e914fb6c, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 27fc360e-fa24-3dff-9bfe-79a065a3b88e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town b883ad5c-d027-4583-bd31-cf09b80448de, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player aa0974cf-bafe-3f00-bd4a-65b146355415 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 04d2ca7a-2895-4135-9fe9-bed8bf252b82, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0ef61a2a-d91b-37b4-a10d-af67b917d543 with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3b7f071f-4ae7-4fb1-a420-9df4a30a0577, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 56a50a28-2026-3b27-8279-243e3ff82a5a with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c05f7e64-51ec-4c7b-9853-5c0792891dd0, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1e92cadb-e057-30f8-a789-5ce10551ba1e with rank Resident
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 61e12bd6-3ac2-4033-894d-78869890173f, firing player update events for 1 players
[15:16:44] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 16deb2db-fd20-3caa-97b8-af2729ef2158 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d1587dea-b047-3ee8-b46a-f3d599824159 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 483238da-c158-4139-b084-9d533f5718ac, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player dbef6173-b792-3041-821b-b11445f44ee8 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town feaef9f1-3244-4dec-acaf-22899d837df7, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 28de3746-47a4-326a-afac-3983252542f7 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e8bd1386-9e2c-4865-b9e2-3a58d5e22f82, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 22dbc6e5-f5c0-33a5-9d21-617eb950afc0 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8259edfd-51df-4a27-b0d6-47aa85fb2073, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e7c5c79a-91ee-3db1-afe8-a6ff4d4f15fa with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5098f6b6-5b18-3f91-8634-ca13195fea22 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town cabe8ac7-596d-491c-91de-3b4ce8a66892, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4cfc4855-b71c-3972-8d90-ac14f3222038 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 70481211-c3b9-44a0-ba28-6940bcb26a34, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a2d3cb04-2e77-32ea-a568-7872ba7e9c5c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8bcf849e-3b9f-45ce-888d-ff3abe156a0c, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 210893c2-ab0c-3341-8a41-02d79e29f9a4 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 26f52792-3e06-45a4-a84d-39ffa6585381, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 387e1557-1e63-3242-8750-2853d61db146 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 75c4e2af-d0a7-4da7-9292-0cc6250de180, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8b8434b7-4246-3db8-89e4-a51a52bc8fc9 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 4cd60086-5feb-4f59-aa1a-d1989fc01c14, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a000e289-fff2-30ee-8de9-0ce5e6f063aa with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 18a45882-f713-4b73-9217-94a805ac35e0, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 28507737-102c-35fd-ac17-02009db33c0a with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ce1eec03-90dd-4e55-aeb9-e402e83f88a4, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player dcea94fd-efa4-3c29-93d8-2d56666245c7 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 100982b7-1713-47f8-b1b7-e9e66cdf706d, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 75998694-2bae-3280-972d-d76ffee01fa1 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town fa316ed1-964f-4935-977e-9902843af565, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ad740e1c-84c3-3b16-9064-6e382083a878 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c551b58b-33cc-47f2-b6b0-2dcf154b906e, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player baf6de3a-5bd7-32fc-ab9f-e6b63b25393f with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 398b6c99-8cb1-4ed1-8c38-08623d693883, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f534a8d0-34b9-3b2c-ae27-c51aded7151b with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e6568df3-4194-44d1-9846-44afadaebd6e, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 588419cf-d7d5-3f7b-8fe7-89996b3b7551 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 5b56da19-9d90-4027-b4d7-37f49dd40ac4, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e62c1c20-738a-32fb-af6c-c761d0c6e0d6 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town feea5a92-9db6-4071-9c56-053fae2e7c35, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7fef5833-06c9-398a-84c0-fbd3a81ff548 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 16ba766f-660b-446c-83ee-88fcaf32a446, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player bc3e28b6-7ce2-3598-9156-cbc22efaa5b0 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town fd030878-1735-4865-91ce-f39925b5c51e, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fe7f1e59-f19d-3f45-941b-fff40e9a414c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 45971005-1752-45a4-80da-ae2ddff4aebf, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3a8a4803-5263-381c-9fa0-2dc262c021aa with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f93c96c3-740c-4116-8e18-57737635fd63, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 98baa43b-a62e-3d66-8ee8-0933f53e227d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 037d4124-446d-441d-8523-ac61548b293b, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6b411cc5-cb23-3aef-b8b1-6cb73d96a2d5 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player abd58e08-2462-379a-81f3-fa94df810a52 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9336d7bc-c90e-4ddd-b298-cbce4a56dd87, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7a3400be-e31a-307d-9fe4-43aac536397b with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c029c77a-2c4f-4dcb-bcae-7b79b02671f8, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 355b2127-0f2e-3f20-b6c2-8997b4abcafb with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a519cc83-44ed-4853-b0fa-101caa456766, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1a5e202b-4f68-3cab-a1c2-b7c265b33cf5 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8aad57c3-16ed-4a95-9422-436154898639, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4c4af65a-e991-33a8-a762-2d145c215578 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town aa09191d-39e3-4375-b0b8-be26bce11118, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player acd62baf-1f62-3e9e-a7f5-5e27309e35be with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 24c4f956-bc10-4cce-b33e-1c136f58e3df, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ab110892-c6b2-3de5-8fa5-f01e37cd4065 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 1f0132cb-89fb-452b-bdfc-7a3141593568, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8fc96dcb-3302-37b6-b952-8c31edd90f80 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3e1fbeae-13f8-3fbb-9842-63bc739da918 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town bd55053d-02ed-4802-b2fc-121047bd4722, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fd6f2cd7-e2c4-325a-9688-114cce28bfc9 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c73d558c-d848-49cd-956a-9196a7df24e3, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e2b9cbcf-86ca-37af-a4ef-9732161cb042 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9b41ee08-47e3-4ec5-b69d-21011c15e31e, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8fbcb74c-bd8d-3181-a022-3c858a10477d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 6415c670-1162-4e39-9be8-5d67e9332478, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c03da6a3-8a6c-3375-a85a-97a8fae94902 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d42f4ef4-ff82-4347-a77c-d322040ff0b2, firing player update events for 10 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 25dc5eb7-e593-3192-87a1-eb20288493eb with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fb4c6c18-26a1-3d95-a13f-9a47871171a2 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 651bbe0e-980e-38d7-8fbb-83ebfec85347 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 83f835f2-20fa-325d-8039-400fcc96db8c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 221c55bf-67e1-303c-b4e6-e7a280e2f738 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5d4844c1-110f-375f-8c73-6803b7a61e5d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2f4f52ff-0d3a-36b8-836b-d09e6352ef45 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7d36885a-8062-3bf0-bbc7-0ea5700f9d67 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d31b2f98-898c-3f1b-b785-db56ff276dc2 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player dc4e436a-deac-3fcd-b3ce-7013be63ef4d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 7448d1e9-4f39-4475-adcf-e1478168e7f3, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 57e72990-8f4d-370a-9882-2dee063faae3 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 7ca97c57-ea96-4c08-8e42-c6d5c004720c, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a0803941-42b2-30d8-878b-d5bba587b8ae with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 6a557cc3-fb5f-427a-a926-bafe2d3a0a56, firing player update events for 0 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ef7bd278-fff3-44f9-8dd5-9f4ec7be1424, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3e97bd98-3fed-36a5-a2b2-bf93d544eeed with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d812caf2-0303-4df8-8e53-ebc00de73364, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c71f5be7-d736-3c74-8462-9246a49acdfd with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ee5fafb3-4693-4286-bf63-5aa778375907, firing player update events for 5 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fe4bc08c-19e6-3b63-ba18-653e7a90e84a with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 15ed0ee5-be80-3fc7-a560-57720b198cde with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 26d3a8a7-c365-3f1b-98bd-1e86d16aa724 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0ee0c13d-70e5-3031-ae99-c5b7b6d44331 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player cb719797-db6c-3a6a-83fb-82a743314305 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3ee54270-2c98-441f-8906-5a9238ed9a66, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f2937d48-a72f-3375-bb6f-69c5f204d185 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 40af2c0b-19d3-47d2-a1ce-72eb435e53d3, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3247b050-58df-3a7e-91f5-44a3d807681c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ecdedce2-34d7-43e2-ae6c-397a4f759279, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b7a940e4-3cde-3275-9c73-2f71fe593c98 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 00981050-1af7-4fe0-aa17-a4d83fdc07d8, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3d66a28d-6934-3ed5-8300-c1ada16fe77b with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 29172492-27b5-4802-884f-21ec817307f6, firing player update events for 0 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0a6490c2-9ec7-4578-96c4-c05c0e4046d4, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7eabe93c-dd7d-38e8-9271-2dd9ed4d2fa5 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a65645a5-9915-46ab-a4e5-4a3eafa7aa26, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 600ef575-e50b-3323-b0f1-19e3a176b08b with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6b45a986-a4aa-3b68-85a3-c5041d0b6192 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d40872af-b1f5-431d-b0f7-580fa07c67dc, firing player update events for 3 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 613fffc1-d252-35dc-8bd1-c9c1b7febbb1 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b1792265-831a-380a-8487-8a743941d06d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ac813ddb-b155-3ee2-ab2a-d375fb559a6a with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e720508e-64b6-4294-8179-d176cf6c39f1, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 54b70f4a-db80-304b-b6ee-afcd086c6623 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e40ab16d-fc22-4a3e-8c88-3f44a25383fa, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e687d94c-f442-3ab7-a8a6-e1baf5dca36c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fcaee4c1-1fdb-389e-bc03-5d6c73d69c2a with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 975c2700-1680-4cc5-8acc-9af93162426d, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 598e33a2-e636-3db6-ba2f-6b9836ee1bdd with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town cc637ba9-3435-4ac1-8e37-aff6a2780fa4, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 53335878-3f56-39c8-893b-77b74c9ca2e1 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d6e922b7-7cc1-35be-bcee-1c10dacdb1bc with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 272cef11-acb6-4ee0-88f2-3f1f3e3c249c, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player be5d3ae9-cfb8-3e0e-818b-c883ef6066be with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town b53ee7aa-7d5f-4052-af2b-43114c79cef2, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 27630cc8-9d92-34f7-8c10-85caa979d8b3 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 678bd7c2-b672-4c9f-985d-f978d3584ff2, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 531f93d1-831a-358c-9bf3-ee2b829257f5 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 976bc1ed-8f12-4c33-aac7-6bc69cd19558, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f594da2a-3dc9-39c1-9261-0e67d02259fd with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 33e9440c-90db-3f3e-bf40-dab751114d4d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d1a76729-1ccb-3de3-b2b3-c7efa54f7c6f with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a2f28896-5544-4b5d-b50c-fbd99a2e77b5, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3d9ab571-1ea5-360b-bc9d-77cd0b2f72a9 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 264224c1-6867-4502-8b49-7aaa42aef79f, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6a350d2f-62d7-3aa3-951b-7fe208d8b2cb with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ea59a2f8-3ef2-41d0-be76-e78be54ee805, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b5db5cb4-9b97-36ff-8827-c3d6087a4cc7 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d96582db-2363-4cdd-ad68-fd69840e7139, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e1c2252e-de3a-347e-acbe-9244e7d23afc with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ffa7b015-7803-48af-871a-e31eec921abf, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 80fb4b31-a429-3891-a185-827168eac297 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d7c3103f-c296-4e38-8900-c087f6d91d3b, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 50441446-173a-3648-92c5-519e7eb2f5c1 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ac10c935-e61e-47c5-8e1c-ec5d3a286daf, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 49b06ace-053b-361b-9d2b-2481cac53c1e with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f51f5d15-e98a-422e-805d-8785df82bcb4, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8dbdaed5-4750-31ee-aa4f-4c3b2bfbc6f7 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 4995ef19-314c-4a36-a400-6bf82e748d3a, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5b0c46c0-894b-30a6-bd31-bf02da63882f with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5b6052a7-6bab-33d6-92c2-a3d77755ef06 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c3e140c2-8b05-4b14-997f-863ed53c000f, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7666ee64-0e62-3b6a-bb71-85f97c5e0623 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f247d133-8e78-400d-b326-a382fc76969f, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b743052f-5855-37e7-a97b-fd551acafef4 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town cc60a66a-494b-482f-9eab-410b179ae619, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 84e9e4f6-df8d-3fc9-b2c7-0eb3b9531d39 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 13b44232-8023-4bce-bcbc-186ad6f9070b, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 29aa321a-1ce7-35de-bfab-f37fa4095c7d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 2f388ab9-8161-4f4e-8ffb-1a65afe5449a, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6ef60f0d-ee89-3acd-9711-6e64aaf89fef with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player babe9e7f-35b2-34ef-82f9-4869d34a0755 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town bd36e320-c182-46a4-ba60-cc3ba4a0749c, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 538c2a55-7afc-37c7-ab76-b8f0ee8467dd with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 27903c96-6327-3ab4-9463-21017fd8955d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 1cb5ad85-59b9-4095-825f-1364ef6d291d, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 13e3d50f-de22-3147-8516-e5bdfb7eb52c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 1306a6ad-3ea4-4106-b4ab-09d645770558, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 90bb5d97-9dd4-3b7b-a71d-c51bbf8a4878 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0f5b2980-3acc-4024-b5b4-19450a419fdf, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 576fba63-4cb8-33cc-820f-ddd80d1ec427 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 62796a47-945e-4936-9a95-4fa450ed07d6, firing player update events for 3 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9662db8b-9062-3256-9f5d-83caa676c3d7 with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 24078ad8-d7e0-3e5c-a4a9-a3bc169a7177 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fc1768ff-17ae-39e8-bef2-f3e0d98306ba with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 16547467-d690-417e-bce9-4cd58a25c298, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 58b4dcc6-56fd-3122-a1b7-3d72cc687631 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 408d12c9-559c-3212-bca5-d1a3fc38a0f7 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0c39f35c-a615-4550-ab9f-e295f8be70cb, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 549b60df-91f3-34d3-a6e5-12d11bc37565 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d944aee7-2684-33ce-b4ec-6dde00bdf73f with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a6007f69-3f95-4ecd-acde-d16d6ad62035, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 76a85934-d1dc-3e22-b48f-18504df540b9 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town af66689a-c04b-4249-a424-a710376856c5, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2c9d87c8-64e7-3ca8-861e-e4a9ef3d93bc with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 031efbcf-d63a-42ae-93f6-595fa590e61b, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f2ba1573-0c0d-3978-bb86-e2dc9c0eb9a4 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f7b38e66-7063-48e5-b35a-48877a960372, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5606c356-f83a-3346-9cfd-7a4fa8e27776 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a6dd2c36-80bb-4c61-8ea2-865ea919fabf, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 74489f5c-5bce-3f80-9a7b-77c68ca90330 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 00000cbc-9402-4ef1-b2e3-ba097c34fdfa, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 42c8facc-cbd2-36d2-8ac3-7dd20c6ce2a1 with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0256ccaa-f6f6-478b-bb23-0ebce2fcb993, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c35c79fa-c14d-3173-a5f4-409e5a0c65eb with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 59be9116-4344-48dc-a300-fd54b50cb51f, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6be062b2-d5f9-3e2d-be7e-7d0540b46d78 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9b47b464-ef47-4123-8afe-b5da2a019d1b, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3b1946fe-c2d1-3fab-8b74-bd39d8b52fb4 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9e221a54-f864-4c17-9f6b-1d3b7f8350ad, firing player update events for 3 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 03ba29a1-d6bd-32ba-90b2-375e4d65abc9 with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5b615aa5-0042-3651-8971-2294273b0cec with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 79804575-3cdf-34b1-8aa3-c1036031e749 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town e1c23312-3859-40b2-ba18-624bd0d595c3, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a04881cc-bc13-39a4-a78b-026264bdd98d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 73181018-6474-36f0-8722-c7fb6befd317 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 809bbcd3-c153-461e-a399-1ad841c4260c, firing player update events for 12 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 68cf6d02-03bc-3f3b-ab98-76a8ec749597 with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 05d3c308-6531-310d-988c-a6164eaf800d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5d36a45e-9473-37e3-8760-623ba7be7a5c with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e10ba356-0952-343e-8ccb-a3b6e69bf75e with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b5079939-0881-3d3d-a86e-56223944774f with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c1b94a42-98fc-3354-947f-44f786fd56fa with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2854591c-b60f-3cde-b807-30391bb3ef3d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 00b30fb1-4b45-3336-98e4-8adf600e5f5e with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 0cb96bf3-4c77-3186-b066-73e68c05fc28 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player eed453d8-1b3a-3fa3-9fc5-1cf433d73521 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player ae93e61c-5484-3859-844d-955ead0489d9 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player fafe4ed7-8e8e-3030-a8e4-3ca9d4ada870 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 42e9b17d-07df-4fba-bdd2-be676c80274a, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d60321ce-54ba-31e5-bac3-cbb6a732015a with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town aa2110b5-ddd6-4e2d-b17d-765dd51878c7, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e4e5b30f-b972-382c-b5e5-86c2e785a8a6 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e9573f2b-d40d-3861-93a9-09f57907533d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 2442bf9f-8632-4327-b68a-78aa57b420cf, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5d6f5056-9d82-3950-9ee4-6a4e4ed3cf33 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2e271c1a-d081-32c8-843a-f79146152093 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 47668211-a0c8-453a-8658-c35c8acf9cdd, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a240c974-e4f5-311c-82e7-f92bb39b2584 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 27188163-4646-3286-ac8a-969ead2946f4 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town b1ecfb16-e7c9-4919-b2b8-370550252426, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 01d9f825-5509-3b83-aacf-c98ce0dce1c7 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town aceee40b-077b-421b-83ee-36e196d28fc3, firing player update events for 4 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 681f539b-8bb8-3f85-85e5-a2945f6c6539 with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player aed5efd4-551b-3965-bc28-ae21aa072a66 with rank Deputy
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 11e2b7aa-6bda-3ca8-b793-391234896312 with rank Admin Viewer
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 34c6f761-a0d4-3d62-b5ee-df15e4530215 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 23aa3f82-2714-4521-8789-be2b00331013, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2250da04-2be8-3508-9a2e-a09e550d0033 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d84f573a-932a-4af7-9095-719939a07fa4, firing player update events for 2 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b24e58a9-7e4c-3f19-91fc-3074c8cfe34b with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player e93afb92-bc6e-34ce-b382-da5f2dd619b2 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 358a13f3-a1db-4741-8235-71300fd72993, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d96be705-d0ae-31d3-afba-31a637d80f6d with rank Resident
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 75c433cf-7bcb-43eb-8d42-c782aa793f38, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6e05ecd8-b367-3929-8021-d89bbb1d2225 with rank Resident
[15:16:45] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f69a3537-b316-401e-8412-303a459930be, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1ef891cf-32a6-31f4-b463-6277834cb67f with rank Mayor
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town ae0e56ab-942d-43ab-8a30-7c16be4a98fe, firing player update events for 1 players
[15:16:45] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b407f19b-cb0e-3acd-945a-df58aa7d7c7d with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 21638641-943d-4c4b-a075-f3abcc46797f, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d6021e41-7343-3bee-bb0e-33bf78c6e47d with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 157dadc9-e980-331f-9b9a-60b0f155ef62 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 778bd3d6-bc14-459b-a87a-17869d9e9e04, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8c2b619e-5319-3b2d-81aa-a99343a6c408 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7de0b911-1474-3b51-bbfc-d3e0a184ff98 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player cfe80848-3f92-3fe5-b0c6-0240c95b9208 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 2adf158d-d3bf-4038-8e49-f53541fa73e8, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 352a2219-a032-396a-9556-4b75f1d6ec41 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 253734e7-c9f5-4d35-bcd6-302e42fc0ea8, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 650a4760-687d-3f2e-93ae-3e71e4c042bb with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 079df915-52f6-4e8c-b5c3-54d04e586c07, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b6529468-5313-3ea4-bc60-3e6ea6cabccd with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 732435c6-5e90-41c9-a5a4-207559f9bc51, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f8b3cbc3-8889-3cbd-890b-ed87a36c6fe4 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 067f1e0a-5eef-42cd-981d-65a1ef04392b, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b17d2796-eeb1-3028-a752-0764d6d5550d with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3793b743-654c-46d4-8b68-dcadb593ceda, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 67c0771c-0b10-3f2c-b5d7-77726e52853d with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 67641bc2-d6a5-400b-b469-50ff5df29aa9, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 62902853-7a10-3bb0-a496-11410998cbd4 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9c6f7109-b578-36c3-a272-028e06c40407 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 1b8c6372-517b-4aa5-bf37-e0b10c14897a, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 8f79be04-4db9-3980-ae1b-e87afab46d4e with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 90939159-fabb-462d-9d92-3105cce322e1, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 3e863622-2d77-3b8e-b124-c7923d3d13f8 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town afa43070-a3ac-4134-841f-efc29f43c218, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b52d0c01-e908-3f05-9150-dcf0319ded3d with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town de8544a5-e176-4038-b699-419018a1530d, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c91a898e-0266-33ea-a80c-5a1fbebd3113 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 0e4d4772-3d89-453c-ba71-2426e168ef65, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 1e8814df-4b51-3d88-9dcd-89145d90282f with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 01ace668-ce82-42b3-8f7c-c9a3219d4967, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 02b93f25-6b3e-31ad-b8e5-b502d04aa517 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 25fd387c-35b2-43b1-9df4-95f0149c62c9, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4292534d-f2c8-3960-ae02-8fc77d827539 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 7331902b-ddbb-498e-b365-71d88ae42ca5, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9a0ac3e8-2a19-3175-bbea-119d15dbea78 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 20e20b8f-a9c3-33a3-bcf3-eb6875f0683d with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f07953aa-b16d-3fed-8d8a-d64904ef9606 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f0806c7c-7cb3-4f6a-9447-5d782af01932, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 94cce2b9-df97-3b51-a97b-9838f894b678 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player d1fadcd5-f8ee-389b-ae76-2112e34d591e with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f85bd8b2-1374-424a-89b0-2625bc473bd8, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 09667321-9873-3aba-80be-1829d546aeb1 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 4a7dbb6e-8289-4274-9a11-25a61493c128, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 094b1c52-a449-3e9f-896c-59239b943c71 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a5d6a06b-a9c7-4067-be5e-a0209e201fa0, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a5dec118-1a7c-3774-9b5e-93530c39dc7c with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 83a58a2f-438a-3130-bffe-4ef9d05cc839 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 08db5308-e82e-4ee5-9f66-1f66a5da672b, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player b63c1160-7834-3764-9276-2fb9c5acd6ce with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town c2569049-fcdf-4278-ac80-d1cf35c645db, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 9bd3b5b5-2b26-3f14-8236-0cdd38b7c3b1 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 449546f9-463a-3959-807a-caed6d7d3cca with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town a06dce52-acae-4249-bc9b-eb3840ee3e7e, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 198eff5d-ccf5-3ba5-aac5-eea0074f959b with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9ff3db28-a0d5-45cd-9b59-fd34d9640724, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 7d05f0c7-0fbd-313e-b15f-14e4684ddd72 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 03e91d98-f031-4f21-b5f8-bb413f2cbf1b, firing player update events for 0 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 8a568eb9-0e15-4e8f-8185-96874fe6477e, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player dd69aa73-45c1-3c79-904a-4ef5b22df161 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player cd68c5ef-6a2a-334d-afe7-65c24468cb5c with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player a205b8da-efc6-37ad-8e1d-84c0239cdd21 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 65bbb57e-3c62-37e7-8694-9fa7354b2d85 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 2f9a5448-417b-4f32-8023-44c71886c60c, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 11e8c009-da15-3a5d-81f0-ba0017af511c with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player f5b981a5-256b-3e0a-a7c0-a95965005354 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town d2fa02b5-eea0-495a-949c-d32c8e171be7, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 2f0d3d8c-afbe-358f-b8f3-786d7b0f9259 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 612ff681-0fbe-42d1-b927-d4b9c12a5982, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 4f2b9d55-5b3e-32ee-ad58-c8df44a23337 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 9ee99cde-9bb9-4d9d-83ee-df2342551b7c, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player aa02bf6c-4f02-33d2-9844-c1af5eb9bb36 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 116028e9-21cf-435b-bedd-3745d285c7fa, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 94590a34-bd24-3d07-8605-517566cb65fe with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 31d13c67-f565-4bd0-9c4d-a9ab620a5913, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 610176b0-594f-3c1a-b889-ed063dbc06ea with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town f67d2e01-39e2-4cb0-8180-7dc6c1d78342, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c5f2027d-c05b-3ad0-bbf1-20e3f4a553c8 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town b51e7ecf-d238-48e7-8624-9540bf816a14, firing player update events for 2 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 6dc954f4-181a-3c6c-99e2-951f7202b4c5 with rank Mayor
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 37815b4b-a277-3e88-89c9-687629f7c447 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 2d685456-5807-4590-b4ec-2648a069959b, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player c21e574d-957a-383a-949a-73e33ee66435 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town 220f69b7-1a72-4180-9533-1cb20000f234, firing player update events for 1 players
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 5bf9c219-9b9d-3293-9d0d-11b90d104d35 with rank Resident
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Processed global chunk data: 47 chunks from 34 towns
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received permission data for player: aed5efd4-551b-3965-bc28-ae21aa072a66 with 0 categories
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received claim tag data sync for town: aceee40b-077b-421b-83ee-36e196d28fc3 with 3 tags
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLAIM TAG RESPONSE: Received 3 tags for town aceee40b-077b-421b-83ee-36e196d28fc3
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLAIM TAG RESPONSE: First tag name: 'Public', color: 65280
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received claim tag data response for town: aceee40b-077b-421b-83ee-36e196d28fc3 with 3 tags
[15:16:46] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Processed global chunk data: 47 chunks from 34 towns
[15:16:47] [Render thread/WARN] (Minecraft) Received passengers for unknown entity
[15:16:47] [Render thread/WARN] (Minecraft) Received passengers for unknown entity
[15:16:47] [Render thread/INFO] (Minecraft) Loaded 76 advancements
[15:16:52] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:54] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:55] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:55] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:55] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:55] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:56] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:56] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:57] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:16:58] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:17:00] [Render thread/ERROR] (pokecobbleclaim) Failed to load town image: Could not load image: Corrupt PNG
[15:17:00] [Render thread/INFO] (pokecobbleclaim) Town key (T) was pressed
[15:17:00] [Render thread/INFO] (pokecobbleclaim) Town key pressed, opening town screen
[15:17:00] [Render thread/INFO] (pokecobbleclaim) Opening town screen
[15:17:12] [Render thread/INFO] (pokecobbleclaim) Requesting jobs data from server
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received jobs data response: 9 jobs
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Unemployed (unlocked: true)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Farmer (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Miner (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Blacksmith (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Merchant (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Police (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Builder (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Architect (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Mayor's Advisor (unlocked: false)
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Jobs data loaded successfully. Total jobs: 9
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Applying town config with 1 entries
[15:17:12] [Netty Epoll Client IO #1/WARN] (pokecobbleclaim) Unknown town setting: imageSettings
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Applied town settings for aceee40b-077b-421b-83ee-36e196d28fc3: {image=default, maxPlayers=20.0, isOpen=true, name=wadaw, description=A lovely town, imageSettings={offsetX=0.0, offsetY=0.0, scale=1.0}}
[15:17:12] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Applied town settings for 1 towns to client
[15:17:12] [Render thread/INFO] (pokecobbleclaim) CLIENT: Updated claim count for town wadaw from 0/4 to 0/4
[15:17:16] [Render thread/INFO] (pokecobbleclaim) Town key (T) was pressed
[15:17:16] [Render thread/INFO] (pokecobbleclaim) Town key pressed, opening town screen
[15:17:16] [Render thread/INFO] (pokecobbleclaim) Opening town screen
[15:17:17] [Render thread/INFO] (pokecobbleclaim) Requesting jobs data from server
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received jobs data response: 9 jobs
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Unemployed (unlocked: true)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Farmer (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Miner (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Blacksmith (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Merchant (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Police (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Builder (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Architect (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Loaded job: Mayor's Advisor (unlocked: false)
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Jobs data loaded successfully. Total jobs: 9
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Applying town config with 1 entries
[15:17:17] [Netty Epoll Client IO #1/WARN] (pokecobbleclaim) Unknown town setting: imageSettings
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Applied town settings for aceee40b-077b-421b-83ee-36e196d28fc3: {image=default, maxPlayers=20.0, isOpen=true, name=wadaw, description=A lovely town, imageSettings={offsetX=0.0, offsetY=0.0, scale=1.0}}
[15:17:17] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Applied town settings for 1 towns to client
[15:17:17] [Render thread/INFO] (pokecobbleclaim) CLIENT: Updated claim count for town wadaw from 0/4 to 0/4
[15:17:20] [Render thread/INFO] (pokecobbleclaim) Requesting jobs data from server
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town aceee40b-077b-421b-83ee-36e196d28fc3, firing player update events for 3 players
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 681f539b-8bb8-3f85-85e5-a2945f6c6539 with rank Mayor
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 11e2b7aa-6bda-3ca8-b793-391234896312 with rank Admin Viewer
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 34c6f761-a0d4-3d62-b5ee-df15e4530215 with rank Resident
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: handleTownListResponse called - packet received!
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town list update with 213 towns
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwa (ID: bc555394-bbf0-475a-95a1-0b17c9d36c0f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwadawa (ID: 853b9a47-40bf-4521-b523-868339a8a0ee)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadw (ID: 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawdaw (ID: 6a314cd9-189f-4b6e-864d-81aa21f6af0f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwad (ID: 6c57a9a0-6448-452d-a814-b8d9040e6043)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadw (ID: 20ede486-11b9-4d1f-b8cc-202ccc0baa34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdssadasd (ID: d72feaa8-f4c1-4532-a294-8e00634a0dbf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwaaw (ID: e91a1bc1-2579-49dc-802b-cf19d448d911)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadwadaw (ID: 906f24a7-d1ca-455c-aca0-dbc94dbed0a8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a a a a aa a  a a a a a aa (ID: 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adawdawd (ID: dcf00911-67f1-4159-9367-6c4c81c89f88)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaadasdasdad (ID: 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdrewrghr (ID: 6e99a3dd-33fe-4563-981f-05d4583d920a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadw (ID: a39c6ace-b46a-47d1-bd08-5fbfc078aa17)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adjsa (ID: be0d82f4-3880-4b9e-a3eb-5e747ebaeef3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadwa (ID: 669d5f05-6ef9-4e7f-a17b-ca50fc162411)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawda (ID: 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawd (ID: d9786d4d-fc23-4454-af9f-f9adafa9bdec)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waaaaaaaaaaaaptssssssssssss (ID: 464f7431-c572-4387-8f91-4edb6613f7e4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawd (ID: 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwaa (ID: f10db762-14e2-4e7e-aa38-41b601cda088)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdwadwa (ID: 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ougaga (ID: 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testicule (ID: b0d2f8dd-3094-42ab-bee1-07f2579bb4ff)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwdada (ID: 9a5193c2-4fea-49c7-a4d6-d8709e7de329)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadadwdawd (ID: 9c1b8f4a-051c-4a69-9c92-5ac16b610544)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadasda (ID: 17af5a11-bd38-41e4-a574-70779fdb21ee)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwada (ID: 78f54b6b-e425-4d18-9933-469118c7019d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwa (ID: 9248516c-92ac-4cd0-8cdf-1dc330367029)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessir (ID: f0e54b62-4630-4c36-b388-f8caa0ee9e71)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadaw (ID: 88840423-accd-4f2e-8fca-d372b807cda7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwawd (ID: 032985e0-551d-422f-9893-e791ea418ce6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdw (ID: 741e6313-004f-46bb-ba8c-cee7a6b12f43)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdwadawda (ID: 4d60dd17-44c1-4e97-9888-32e563b56199)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanusima (ID: 05e3661f-4790-4fc7-8368-85bc77f1aeef)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - addawdadwad (ID: 733810c2-ee73-4c36-8f01-f32508c7b33f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaa (ID: 6f823d06-f53d-478b-b50e-766154019f39)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasa (ID: 27907c34-f576-4d02-b00d-ae74912e0ce2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdwad (ID: 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ticsssss (ID: 0250cce5-1df9-4fb2-9cce-04b98f710590)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdad (ID: 08fda338-bbea-426e-acd8-916d2e3d0fa5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdawd (ID: 0f27042d-3329-4823-9fb7-bbf7455feb6c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawda (ID: 5b2b5170-839e-48e7-9363-c5749cb7cff1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddwadwadw (ID: dcad3d2a-859d-48a6-96ea-a1e0a9c5840b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawda (ID: 8d5a42ff-95fd-48a8-b128-5d9776ebe6de)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - safwef (ID: a68bf3e5-5d2f-436d-8d5a-0410d5c285d9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadaw (ID: 024247bf-9275-4b17-9615-4ee89f4529a3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test1000 (ID: 1c41e035-1e9c-4508-9095-8b85fd99b3ed)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - alo (ID: 9bd7ba35-3161-4175-b0a1-bab05aff119c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadasdadasd (ID: 853f9480-d66b-4dfa-985e-2a4d71339b88)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdadw (ID: 5abfa938-5fc3-4d16-b0da-fbbda5929320)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaaa (ID: 50c8de1a-75e0-49b1-b98a-b1eb195a7331)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadaws (ID: a829eda3-08b2-4d0d-a59d-e94f2c590635)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawd (ID: 77198cd6-f6e9-47d5-9195-b53da486c253)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwawa (ID: e8f21959-b887-4d17-b8bc-c21ca9c67bd2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaa (ID: e39ae71e-3f67-4511-9f30-5ecca1ab9a34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadwada (ID: 24de29f1-549b-46a3-81aa-1e50e914fb6c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaw (ID: b883ad5c-d027-4583-bd31-cf09b80448de)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a s s sd d  d d d d d d d d d (ID: 04d2ca7a-2895-4135-9fe9-bed8bf252b82)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwa (ID: 3b7f071f-4ae7-4fb1-a420-9df4a30a0577)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdawd (ID: c05f7e64-51ec-4c7b-9853-5c0792891dd0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadasdaw (ID: 61e12bd6-3ac2-4033-894d-78869890173f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - DWDAWA (ID: 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwadwad (ID: 483238da-c158-4139-b084-9d533f5718ac)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdaw (ID: feaef9f1-3244-4dec-acaf-22899d837df7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawwad (ID: e8bd1386-9e2c-4865-b9e2-3a58d5e22f82)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaa (ID: 8259edfd-51df-4a27-b0d6-47aa85fb2073)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwdwa (ID: cabe8ac7-596d-491c-91de-3b4ce8a66892)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pooeooooooooooooooo (ID: 70481211-c3b9-44a0-ba28-6940bcb26a34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadaw (ID: 8bcf849e-3b9f-45ce-888d-ff3abe156a0c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdawd (ID: 26f52792-3e06-45a4-a84d-39ffa6585381)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: 75c4e2af-d0a7-4da7-9292-0cc6250de180)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwad (ID: 4cd60086-5feb-4f59-aa1a-d1989fc01c14)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - tee (ID: 18a45882-f713-4b73-9217-94a805ac35e0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadw (ID: ce1eec03-90dd-4e55-aeb9-e402e83f88a4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawa (ID: 100982b7-1713-47f8-b1b7-e9e66cdf706d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wDwdadww (ID: fa316ed1-964f-4935-977e-9902843af565)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwa (ID: c551b58b-33cc-47f2-b6b0-2dcf154b906e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdawdawdaw (ID: 398b6c99-8cb1-4ed1-8c38-08623d693883)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm (ID: e6568df3-4194-44d1-9846-44afadaebd6e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwawdwa (ID: 5b56da19-9d90-4027-b4d7-37f49dd40ac4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawda (ID: feea5a92-9db6-4071-9c56-053fae2e7c35)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawda (ID: 16ba766f-660b-446c-83ee-88fcaf32a446)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: fd030878-1735-4865-91ce-f39925b5c51e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwa (ID: 45971005-1752-45a4-80da-ae2ddff4aebf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ouggggggggggg (ID: f93c96c3-740c-4116-8e18-57737635fd63)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - jean paul (ID: 037d4124-446d-441d-8523-ac61548b293b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasdweas (ID: 9336d7bc-c90e-4ddd-b298-cbce4a56dd87)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadawda (ID: c029c77a-2c4f-4dcb-bcae-7b79b02671f8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwaaw (ID: a519cc83-44ed-4853-b0fa-101caa456766)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test3 (ID: 8aad57c3-16ed-4a95-9422-436154898639)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdadaw (ID: aa09191d-39e3-4375-b0b8-be26bce11118)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsfdfsfs (ID: 24c4f956-bc10-4cce-b33e-1c136f58e3df)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdadsa (ID: 1f0132cb-89fb-452b-bdfc-7a3141593568)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yesssssssssss (ID: 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawda (ID: bd55053d-02ed-4802-b2fc-121047bd4722)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasfa (ID: c73d558c-d848-49cd-956a-9196a7df24e3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwadwad (ID: 9b41ee08-47e3-4ec5-b69d-21011c15e31e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadawda (ID: 6415c670-1162-4e39-9be8-5d67e9332478)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwdaaa (ID: 7448d1e9-4f39-4475-adcf-e1478168e7f3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - afgee (ID: 7ca97c57-ea96-4c08-8e42-c6d5c004720c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawwda (ID: ef7bd278-fff3-44f9-8dd5-9f4ec7be1424)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawa (ID: d812caf2-0303-4df8-8e53-ebc00de73364)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssssssssssssssssssssssssssss (ID: ee5fafb3-4693-4286-bf63-5aa778375907)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdaawa (ID: 3ee54270-2c98-441f-8906-5a9238ed9a66)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwa (ID: 40af2c0b-19d3-47d2-a1ce-72eb435e53d3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwaaw (ID: ecdedce2-34d7-43e2-ae6c-397a4f759279)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdaa (ID: 00981050-1af7-4fe0-aa17-a4d83fdc07d8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssss (ID: 29172492-27b5-4802-884f-21ec817307f6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawa (ID: 0a6490c2-9ec7-4578-96c4-c05c0e4046d4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwa (ID: a65645a5-9915-46ab-a4e5-4a3eafa7aa26)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanus (ID: d40872af-b1f5-431d-b0f7-580fa07c67dc)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdadwa (ID: e720508e-64b6-4294-8179-d176cf6c39f1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pinis (ID: e40ab16d-fc22-4a3e-8c88-3f44a25383fa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwadfsdasd (ID: 975c2700-1680-4cc5-8acc-9af93162426d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdaw (ID: cc637ba9-3435-4ac1-8e37-aff6a2780fa4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - annus (ID: 272cef11-acb6-4ee0-88f2-3f1f3e3c249c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawda (ID: b53ee7aa-7d5f-4052-af2b-43114c79cef2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwad (ID: 678bd7c2-b672-4c9f-985d-f978d3584ff2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwadwadaw (ID: 976bc1ed-8f12-4c33-aac7-6bc69cd19558)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - 00000000000000000000000000000000 (ID: 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: a2f28896-5544-4b5d-b50c-fbd99a2e77b5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasasayaaaaaaaaaaaaaaaaaaaaa (ID: 264224c1-6867-4502-8b49-7aaa42aef79f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaw (ID: ea59a2f8-3ef2-41d0-be76-e78be54ee805)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessirk (ID: d96582db-2363-4cdd-ad68-fd69840e7139)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdas (ID: ffa7b015-7803-48af-871a-e31eec921abf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawaw (ID: d7c3103f-c296-4e38-8900-c087f6d91d3b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdawdwad (ID: ac10c935-e61e-47c5-8e1c-ec5d3a286daf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwwdwa (ID: f51f5d15-e98a-422e-805d-8785df82bcb4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadw (ID: 4995ef19-314c-4a36-a400-6bf82e748d3a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawd (ID: c3e140c2-8b05-4b14-997f-863ed53c000f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsafasfaf (ID: f247d133-8e78-400d-b326-a382fc76969f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wda (ID: cc60a66a-494b-482f-9eab-410b179ae619)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwad (ID: 13b44232-8023-4bce-bcbc-186ad6f9070b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testtttttttttttttttttttttttttttt (ID: 2f388ab9-8161-4f4e-8ffb-1a65afe5449a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadawdad (ID: 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: bd36e320-c182-46a4-ba60-cc3ba4a0749c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadaw (ID: 1cb5ad85-59b9-4095-825f-1364ef6d291d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - diuashduihawuihduia (ID: 1306a6ad-3ea4-4106-b4ab-09d645770558)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsafasfs (ID: 0f5b2980-3acc-4024-b5b4-19450a419fdf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 62796a47-945e-4936-9a95-4fa450ed07d6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasad (ID: 16547467-d690-417e-bce9-4cd58a25c298)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawaad (ID: 0c39f35c-a615-4550-ab9f-e295f8be70cb)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwawdwa (ID: f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdaa (ID: a6007f69-3f95-4ecd-acde-d16d6ad62035)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsadwerwffsdfssssss (ID: af66689a-c04b-4249-a424-a710376856c5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwa (ID: 031efbcf-d63a-42ae-93f6-595fa590e61b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawaw (ID: f7b38e66-7063-48e5-b35a-48877a960372)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaa (ID: a6dd2c36-80bb-4c61-8ea2-865ea919fabf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daasa (ID: 00000cbc-9402-4ef1-b2e3-ba097c34fdfa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: 0256ccaa-f6f6-478b-bb23-0ebce2fcb993)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwd (ID: 59be9116-4344-48dc-a300-fd54b50cb51f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwaa (ID: 9b47b464-ef47-4123-8afe-b5da2a019d1b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaa (ID: 9e221a54-f864-4c17-9f6b-1d3b7f8350ad)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwada (ID: e1c23312-3859-40b2-ba18-624bd0d595c3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwawda (ID: 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fDAF (ID: 809bbcd3-c153-461e-a399-1ad841c4260c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdaw (ID: 42e9b17d-07df-4fba-bdd2-be676c80274a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdw (ID: aa2110b5-ddd6-4e2d-b17d-765dd51878c7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsaaaaaaaaaaaa (ID: 2442bf9f-8632-4327-b68a-78aa57b420cf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 47668211-a0c8-453a-8658-c35c8acf9cdd)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa33333 (ID: b1ecfb16-e7c9-4919-b2b8-370550252426)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadaw (ID: aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddawdwa (ID: 23aa3f82-2714-4521-8789-be2b00331013)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawda (ID: d84f573a-932a-4af7-9095-719939a07fa4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdaw (ID: 358a13f3-a1db-4741-8235-71300fd72993)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdada (ID: 75c433cf-7bcb-43eb-8d42-c782aa793f38)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwad (ID: f69a3537-b316-401e-8412-303a459930be)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawadasd (ID: ae0e56ab-942d-43ab-8a30-7c16be4a98fe)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdfdsdfa3343224324 (ID: 21638641-943d-4c4b-a075-f3abcc46797f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 778bd3d6-bc14-459b-a87a-17869d9e9e04)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadasdasd (ID: 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwad (ID: 2adf158d-d3bf-4038-8e49-f53541fa73e8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawd (ID: 253734e7-c9f5-4d35-bcd6-302e42fc0ea8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing (ID: 079df915-52f6-4e8c-b5c3-54d04e586c07)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawdadawd (ID: 732435c6-5e90-41c9-a5a4-207559f9bc51)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadawdawd (ID: 067f1e0a-5eef-42cd-981d-65a1ef04392b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawwda (ID: 3793b743-654c-46d4-8b68-dcadb593ceda)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaawwwwwwwwwwwww (ID: 67641bc2-d6a5-400b-b469-50ff5df29aa9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwaa (ID: 1b8c6372-517b-4aa5-bf37-e0b10c14897a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawa (ID: 90939159-fabb-462d-9d92-3105cce322e1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdw (ID: afa43070-a3ac-4134-841f-efc29f43c218)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sfdsfetdfcfvcbbfghfhfhfdgfgrgf (ID: de8544a5-e176-4038-b699-419018a1530d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaw (ID: 0e4d4772-3d89-453c-ba71-2426e168ef65)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ddddddddddddddddddddddddddddddd (ID: 01ace668-ce82-42b3-8f7c-c9a3219d4967)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing perm (ID: 25fd387c-35b2-43b1-9df4-95f0149c62c9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sad (ID: 7331902b-ddbb-498e-b365-71d88ae42ca5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daw (ID: 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - oui (ID: f0806c7c-7cb3-4f6a-9447-5d782af01932)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadwa (ID: f85bd8b2-1374-424a-89b0-2625bc473bd8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadw (ID: 4a7dbb6e-8289-4274-9a11-25a61493c128)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawddaw (ID: a5d6a06b-a9c7-4067-be5e-a0209e201fa0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawd (ID: 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaaaaaa (ID: 08db5308-e82e-4ee5-9f66-1f66a5da672b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsadasdasd (ID: c2569049-fcdf-4278-ac80-d1cf35c645db)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwdadw (ID: a06dce52-acae-4249-bc9b-eb3840ee3e7e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawdawddaw (ID: 9ff3db28-a0d5-45cd-9b59-fd34d9640724)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 8a568eb9-0e15-4e8f-8185-96874fe6477e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fuk (ID: 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - rrew (ID: 2f9a5448-417b-4f32-8023-44c71886c60c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawddwa (ID: d2fa02b5-eea0-495a-949c-d32c8e171be7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdadw (ID: 612ff681-0fbe-42d1-b927-d4b9c12a5982)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwaad (ID: 9ee99cde-9bb9-4d9d-83ee-df2342551b7c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdadwadsa (ID: 116028e9-21cf-435b-bedd-3745d285c7fa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdaw (ID: 31d13c67-f565-4bd0-9c4d-a9ab620a5913)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwadwad (ID: f67d2e01-39e2-4cb0-8180-7dc6c1d78342)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: b51e7ecf-d238-48e7-8624-9540bf816a14)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dasdawdwa (ID: 2d685456-5807-4590-b4ec-2648a069959b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdad (ID: 220f69b7-1a72-4180-9533-1cb20000f234)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: handleTownListResponse called - packet received!
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town list update with 213 towns
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwa (ID: bc555394-bbf0-475a-95a1-0b17c9d36c0f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwadawa (ID: 853b9a47-40bf-4521-b523-868339a8a0ee)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadw (ID: 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawdaw (ID: 6a314cd9-189f-4b6e-864d-81aa21f6af0f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwad (ID: 6c57a9a0-6448-452d-a814-b8d9040e6043)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadw (ID: 20ede486-11b9-4d1f-b8cc-202ccc0baa34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdssadasd (ID: d72feaa8-f4c1-4532-a294-8e00634a0dbf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwaaw (ID: e91a1bc1-2579-49dc-802b-cf19d448d911)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadwadaw (ID: 906f24a7-d1ca-455c-aca0-dbc94dbed0a8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a a a a aa a  a a a a a aa (ID: 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adawdawd (ID: dcf00911-67f1-4159-9367-6c4c81c89f88)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaadasdasdad (ID: 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdrewrghr (ID: 6e99a3dd-33fe-4563-981f-05d4583d920a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadw (ID: a39c6ace-b46a-47d1-bd08-5fbfc078aa17)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adjsa (ID: be0d82f4-3880-4b9e-a3eb-5e747ebaeef3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadwa (ID: 669d5f05-6ef9-4e7f-a17b-ca50fc162411)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawda (ID: 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawd (ID: d9786d4d-fc23-4454-af9f-f9adafa9bdec)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waaaaaaaaaaaaptssssssssssss (ID: 464f7431-c572-4387-8f91-4edb6613f7e4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawd (ID: 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwaa (ID: f10db762-14e2-4e7e-aa38-41b601cda088)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdwadwa (ID: 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ougaga (ID: 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testicule (ID: b0d2f8dd-3094-42ab-bee1-07f2579bb4ff)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwdada (ID: 9a5193c2-4fea-49c7-a4d6-d8709e7de329)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadadwdawd (ID: 9c1b8f4a-051c-4a69-9c92-5ac16b610544)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadasda (ID: 17af5a11-bd38-41e4-a574-70779fdb21ee)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwada (ID: 78f54b6b-e425-4d18-9933-469118c7019d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwa (ID: 9248516c-92ac-4cd0-8cdf-1dc330367029)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessir (ID: f0e54b62-4630-4c36-b388-f8caa0ee9e71)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadaw (ID: 88840423-accd-4f2e-8fca-d372b807cda7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwawd (ID: 032985e0-551d-422f-9893-e791ea418ce6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdw (ID: 741e6313-004f-46bb-ba8c-cee7a6b12f43)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdwadawda (ID: 4d60dd17-44c1-4e97-9888-32e563b56199)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanusima (ID: 05e3661f-4790-4fc7-8368-85bc77f1aeef)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - addawdadwad (ID: 733810c2-ee73-4c36-8f01-f32508c7b33f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaa (ID: 6f823d06-f53d-478b-b50e-766154019f39)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasa (ID: 27907c34-f576-4d02-b00d-ae74912e0ce2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdwad (ID: 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ticsssss (ID: 0250cce5-1df9-4fb2-9cce-04b98f710590)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdad (ID: 08fda338-bbea-426e-acd8-916d2e3d0fa5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdawd (ID: 0f27042d-3329-4823-9fb7-bbf7455feb6c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawda (ID: 5b2b5170-839e-48e7-9363-c5749cb7cff1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddwadwadw (ID: dcad3d2a-859d-48a6-96ea-a1e0a9c5840b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawda (ID: 8d5a42ff-95fd-48a8-b128-5d9776ebe6de)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - safwef (ID: a68bf3e5-5d2f-436d-8d5a-0410d5c285d9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadaw (ID: 024247bf-9275-4b17-9615-4ee89f4529a3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test1000 (ID: 1c41e035-1e9c-4508-9095-8b85fd99b3ed)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - alo (ID: 9bd7ba35-3161-4175-b0a1-bab05aff119c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadasdadasd (ID: 853f9480-d66b-4dfa-985e-2a4d71339b88)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdadw (ID: 5abfa938-5fc3-4d16-b0da-fbbda5929320)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaaa (ID: 50c8de1a-75e0-49b1-b98a-b1eb195a7331)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadaws (ID: a829eda3-08b2-4d0d-a59d-e94f2c590635)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawd (ID: 77198cd6-f6e9-47d5-9195-b53da486c253)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwawa (ID: e8f21959-b887-4d17-b8bc-c21ca9c67bd2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaa (ID: e39ae71e-3f67-4511-9f30-5ecca1ab9a34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadwada (ID: 24de29f1-549b-46a3-81aa-1e50e914fb6c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaw (ID: b883ad5c-d027-4583-bd31-cf09b80448de)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a s s sd d  d d d d d d d d d (ID: 04d2ca7a-2895-4135-9fe9-bed8bf252b82)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwa (ID: 3b7f071f-4ae7-4fb1-a420-9df4a30a0577)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdawd (ID: c05f7e64-51ec-4c7b-9853-5c0792891dd0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadasdaw (ID: 61e12bd6-3ac2-4033-894d-78869890173f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - DWDAWA (ID: 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwadwad (ID: 483238da-c158-4139-b084-9d533f5718ac)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdaw (ID: feaef9f1-3244-4dec-acaf-22899d837df7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawwad (ID: e8bd1386-9e2c-4865-b9e2-3a58d5e22f82)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaa (ID: 8259edfd-51df-4a27-b0d6-47aa85fb2073)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwdwa (ID: cabe8ac7-596d-491c-91de-3b4ce8a66892)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pooeooooooooooooooo (ID: 70481211-c3b9-44a0-ba28-6940bcb26a34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadaw (ID: 8bcf849e-3b9f-45ce-888d-ff3abe156a0c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdawd (ID: 26f52792-3e06-45a4-a84d-39ffa6585381)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: 75c4e2af-d0a7-4da7-9292-0cc6250de180)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwad (ID: 4cd60086-5feb-4f59-aa1a-d1989fc01c14)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - tee (ID: 18a45882-f713-4b73-9217-94a805ac35e0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadw (ID: ce1eec03-90dd-4e55-aeb9-e402e83f88a4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawa (ID: 100982b7-1713-47f8-b1b7-e9e66cdf706d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wDwdadww (ID: fa316ed1-964f-4935-977e-9902843af565)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwa (ID: c551b58b-33cc-47f2-b6b0-2dcf154b906e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdawdawdaw (ID: 398b6c99-8cb1-4ed1-8c38-08623d693883)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm (ID: e6568df3-4194-44d1-9846-44afadaebd6e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwawdwa (ID: 5b56da19-9d90-4027-b4d7-37f49dd40ac4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawda (ID: feea5a92-9db6-4071-9c56-053fae2e7c35)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawda (ID: 16ba766f-660b-446c-83ee-88fcaf32a446)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: fd030878-1735-4865-91ce-f39925b5c51e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwa (ID: 45971005-1752-45a4-80da-ae2ddff4aebf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ouggggggggggg (ID: f93c96c3-740c-4116-8e18-57737635fd63)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - jean paul (ID: 037d4124-446d-441d-8523-ac61548b293b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasdweas (ID: 9336d7bc-c90e-4ddd-b298-cbce4a56dd87)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadawda (ID: c029c77a-2c4f-4dcb-bcae-7b79b02671f8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwaaw (ID: a519cc83-44ed-4853-b0fa-101caa456766)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test3 (ID: 8aad57c3-16ed-4a95-9422-436154898639)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdadaw (ID: aa09191d-39e3-4375-b0b8-be26bce11118)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsfdfsfs (ID: 24c4f956-bc10-4cce-b33e-1c136f58e3df)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdadsa (ID: 1f0132cb-89fb-452b-bdfc-7a3141593568)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yesssssssssss (ID: 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawda (ID: bd55053d-02ed-4802-b2fc-121047bd4722)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasfa (ID: c73d558c-d848-49cd-956a-9196a7df24e3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwadwad (ID: 9b41ee08-47e3-4ec5-b69d-21011c15e31e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadawda (ID: 6415c670-1162-4e39-9be8-5d67e9332478)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwdaaa (ID: 7448d1e9-4f39-4475-adcf-e1478168e7f3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - afgee (ID: 7ca97c57-ea96-4c08-8e42-c6d5c004720c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawwda (ID: ef7bd278-fff3-44f9-8dd5-9f4ec7be1424)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawa (ID: d812caf2-0303-4df8-8e53-ebc00de73364)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssssssssssssssssssssssssssss (ID: ee5fafb3-4693-4286-bf63-5aa778375907)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdaawa (ID: 3ee54270-2c98-441f-8906-5a9238ed9a66)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwa (ID: 40af2c0b-19d3-47d2-a1ce-72eb435e53d3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwaaw (ID: ecdedce2-34d7-43e2-ae6c-397a4f759279)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdaa (ID: 00981050-1af7-4fe0-aa17-a4d83fdc07d8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssss (ID: 29172492-27b5-4802-884f-21ec817307f6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawa (ID: 0a6490c2-9ec7-4578-96c4-c05c0e4046d4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwa (ID: a65645a5-9915-46ab-a4e5-4a3eafa7aa26)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanus (ID: d40872af-b1f5-431d-b0f7-580fa07c67dc)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdadwa (ID: e720508e-64b6-4294-8179-d176cf6c39f1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pinis (ID: e40ab16d-fc22-4a3e-8c88-3f44a25383fa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwadfsdasd (ID: 975c2700-1680-4cc5-8acc-9af93162426d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdaw (ID: cc637ba9-3435-4ac1-8e37-aff6a2780fa4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - annus (ID: 272cef11-acb6-4ee0-88f2-3f1f3e3c249c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawda (ID: b53ee7aa-7d5f-4052-af2b-43114c79cef2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwad (ID: 678bd7c2-b672-4c9f-985d-f978d3584ff2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwadwadaw (ID: 976bc1ed-8f12-4c33-aac7-6bc69cd19558)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - 00000000000000000000000000000000 (ID: 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: a2f28896-5544-4b5d-b50c-fbd99a2e77b5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasasayaaaaaaaaaaaaaaaaaaaaa (ID: 264224c1-6867-4502-8b49-7aaa42aef79f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaw (ID: ea59a2f8-3ef2-41d0-be76-e78be54ee805)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessirk (ID: d96582db-2363-4cdd-ad68-fd69840e7139)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdas (ID: ffa7b015-7803-48af-871a-e31eec921abf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawaw (ID: d7c3103f-c296-4e38-8900-c087f6d91d3b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdawdwad (ID: ac10c935-e61e-47c5-8e1c-ec5d3a286daf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwwdwa (ID: f51f5d15-e98a-422e-805d-8785df82bcb4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadw (ID: 4995ef19-314c-4a36-a400-6bf82e748d3a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawd (ID: c3e140c2-8b05-4b14-997f-863ed53c000f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsafasfaf (ID: f247d133-8e78-400d-b326-a382fc76969f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wda (ID: cc60a66a-494b-482f-9eab-410b179ae619)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwad (ID: 13b44232-8023-4bce-bcbc-186ad6f9070b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testtttttttttttttttttttttttttttt (ID: 2f388ab9-8161-4f4e-8ffb-1a65afe5449a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadawdad (ID: 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: bd36e320-c182-46a4-ba60-cc3ba4a0749c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadaw (ID: 1cb5ad85-59b9-4095-825f-1364ef6d291d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - diuashduihawuihduia (ID: 1306a6ad-3ea4-4106-b4ab-09d645770558)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsafasfs (ID: 0f5b2980-3acc-4024-b5b4-19450a419fdf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 62796a47-945e-4936-9a95-4fa450ed07d6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasad (ID: 16547467-d690-417e-bce9-4cd58a25c298)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawaad (ID: 0c39f35c-a615-4550-ab9f-e295f8be70cb)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwawdwa (ID: f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdaa (ID: a6007f69-3f95-4ecd-acde-d16d6ad62035)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsadwerwffsdfssssss (ID: af66689a-c04b-4249-a424-a710376856c5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwa (ID: 031efbcf-d63a-42ae-93f6-595fa590e61b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawaw (ID: f7b38e66-7063-48e5-b35a-48877a960372)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaa (ID: a6dd2c36-80bb-4c61-8ea2-865ea919fabf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daasa (ID: 00000cbc-9402-4ef1-b2e3-ba097c34fdfa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: 0256ccaa-f6f6-478b-bb23-0ebce2fcb993)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwd (ID: 59be9116-4344-48dc-a300-fd54b50cb51f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwaa (ID: 9b47b464-ef47-4123-8afe-b5da2a019d1b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaa (ID: 9e221a54-f864-4c17-9f6b-1d3b7f8350ad)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwada (ID: e1c23312-3859-40b2-ba18-624bd0d595c3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwawda (ID: 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fDAF (ID: 809bbcd3-c153-461e-a399-1ad841c4260c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdaw (ID: 42e9b17d-07df-4fba-bdd2-be676c80274a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdw (ID: aa2110b5-ddd6-4e2d-b17d-765dd51878c7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsaaaaaaaaaaaa (ID: 2442bf9f-8632-4327-b68a-78aa57b420cf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 47668211-a0c8-453a-8658-c35c8acf9cdd)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa33333 (ID: b1ecfb16-e7c9-4919-b2b8-370550252426)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadaw (ID: aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddawdwa (ID: 23aa3f82-2714-4521-8789-be2b00331013)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawda (ID: d84f573a-932a-4af7-9095-719939a07fa4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdaw (ID: 358a13f3-a1db-4741-8235-71300fd72993)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdada (ID: 75c433cf-7bcb-43eb-8d42-c782aa793f38)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwad (ID: f69a3537-b316-401e-8412-303a459930be)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawadasd (ID: ae0e56ab-942d-43ab-8a30-7c16be4a98fe)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdfdsdfa3343224324 (ID: 21638641-943d-4c4b-a075-f3abcc46797f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 778bd3d6-bc14-459b-a87a-17869d9e9e04)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadasdasd (ID: 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwad (ID: 2adf158d-d3bf-4038-8e49-f53541fa73e8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawd (ID: 253734e7-c9f5-4d35-bcd6-302e42fc0ea8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing (ID: 079df915-52f6-4e8c-b5c3-54d04e586c07)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawdadawd (ID: 732435c6-5e90-41c9-a5a4-207559f9bc51)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadawdawd (ID: 067f1e0a-5eef-42cd-981d-65a1ef04392b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawwda (ID: 3793b743-654c-46d4-8b68-dcadb593ceda)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaawwwwwwwwwwwww (ID: 67641bc2-d6a5-400b-b469-50ff5df29aa9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwaa (ID: 1b8c6372-517b-4aa5-bf37-e0b10c14897a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawa (ID: 90939159-fabb-462d-9d92-3105cce322e1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdw (ID: afa43070-a3ac-4134-841f-efc29f43c218)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sfdsfetdfcfvcbbfghfhfhfdgfgrgf (ID: de8544a5-e176-4038-b699-419018a1530d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaw (ID: 0e4d4772-3d89-453c-ba71-2426e168ef65)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ddddddddddddddddddddddddddddddd (ID: 01ace668-ce82-42b3-8f7c-c9a3219d4967)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing perm (ID: 25fd387c-35b2-43b1-9df4-95f0149c62c9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sad (ID: 7331902b-ddbb-498e-b365-71d88ae42ca5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daw (ID: 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - oui (ID: f0806c7c-7cb3-4f6a-9447-5d782af01932)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadwa (ID: f85bd8b2-1374-424a-89b0-2625bc473bd8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadw (ID: 4a7dbb6e-8289-4274-9a11-25a61493c128)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawddaw (ID: a5d6a06b-a9c7-4067-be5e-a0209e201fa0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawd (ID: 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaaaaaa (ID: 08db5308-e82e-4ee5-9f66-1f66a5da672b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsadasdasd (ID: c2569049-fcdf-4278-ac80-d1cf35c645db)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwdadw (ID: a06dce52-acae-4249-bc9b-eb3840ee3e7e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawdawddaw (ID: 9ff3db28-a0d5-45cd-9b59-fd34d9640724)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 8a568eb9-0e15-4e8f-8185-96874fe6477e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fuk (ID: 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - rrew (ID: 2f9a5448-417b-4f32-8023-44c71886c60c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawddwa (ID: d2fa02b5-eea0-495a-949c-d32c8e171be7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdadw (ID: 612ff681-0fbe-42d1-b927-d4b9c12a5982)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwaad (ID: 9ee99cde-9bb9-4d9d-83ee-df2342551b7c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdadwadsa (ID: 116028e9-21cf-435b-bedd-3745d285c7fa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdaw (ID: 31d13c67-f565-4bd0-9c4d-a9ab620a5913)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwadwad (ID: f67d2e01-39e2-4cb0-8180-7dc6c1d78342)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: b51e7ecf-d238-48e7-8624-9540bf816a14)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dasdawdwa (ID: 2d685456-5807-4590-b4ec-2648a069959b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdad (ID: 220f69b7-1a72-4180-9533-1cb20000f234)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Restored claim tool access for Player2: false
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Cleared client-side town association for player Player2
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Successfully left town: Successfully left wadaw
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received jobs data response: no town data
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Applying town config with 0 entries
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Applied town settings for 0 towns to client
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: handleTownListResponse called - packet received!
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town list update with 213 towns
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwa (ID: bc555394-bbf0-475a-95a1-0b17c9d36c0f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwadawa (ID: 853b9a47-40bf-4521-b523-868339a8a0ee)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadw (ID: 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawdaw (ID: 6a314cd9-189f-4b6e-864d-81aa21f6af0f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwad (ID: 6c57a9a0-6448-452d-a814-b8d9040e6043)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadw (ID: 20ede486-11b9-4d1f-b8cc-202ccc0baa34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdssadasd (ID: d72feaa8-f4c1-4532-a294-8e00634a0dbf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwaaw (ID: e91a1bc1-2579-49dc-802b-cf19d448d911)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadwadaw (ID: 906f24a7-d1ca-455c-aca0-dbc94dbed0a8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a a a a aa a  a a a a a aa (ID: 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adawdawd (ID: dcf00911-67f1-4159-9367-6c4c81c89f88)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaadasdasdad (ID: 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdrewrghr (ID: 6e99a3dd-33fe-4563-981f-05d4583d920a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadw (ID: a39c6ace-b46a-47d1-bd08-5fbfc078aa17)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adjsa (ID: be0d82f4-3880-4b9e-a3eb-5e747ebaeef3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadwa (ID: 669d5f05-6ef9-4e7f-a17b-ca50fc162411)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawda (ID: 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawd (ID: d9786d4d-fc23-4454-af9f-f9adafa9bdec)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waaaaaaaaaaaaptssssssssssss (ID: 464f7431-c572-4387-8f91-4edb6613f7e4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawd (ID: 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwaa (ID: f10db762-14e2-4e7e-aa38-41b601cda088)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdwadwa (ID: 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ougaga (ID: 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testicule (ID: b0d2f8dd-3094-42ab-bee1-07f2579bb4ff)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwdada (ID: 9a5193c2-4fea-49c7-a4d6-d8709e7de329)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadadwdawd (ID: 9c1b8f4a-051c-4a69-9c92-5ac16b610544)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadasda (ID: 17af5a11-bd38-41e4-a574-70779fdb21ee)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwada (ID: 78f54b6b-e425-4d18-9933-469118c7019d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwa (ID: 9248516c-92ac-4cd0-8cdf-1dc330367029)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessir (ID: f0e54b62-4630-4c36-b388-f8caa0ee9e71)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadaw (ID: 88840423-accd-4f2e-8fca-d372b807cda7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwawd (ID: 032985e0-551d-422f-9893-e791ea418ce6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdw (ID: 741e6313-004f-46bb-ba8c-cee7a6b12f43)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdwadawda (ID: 4d60dd17-44c1-4e97-9888-32e563b56199)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanusima (ID: 05e3661f-4790-4fc7-8368-85bc77f1aeef)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - addawdadwad (ID: 733810c2-ee73-4c36-8f01-f32508c7b33f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaa (ID: 6f823d06-f53d-478b-b50e-766154019f39)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasa (ID: 27907c34-f576-4d02-b00d-ae74912e0ce2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdwad (ID: 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ticsssss (ID: 0250cce5-1df9-4fb2-9cce-04b98f710590)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdad (ID: 08fda338-bbea-426e-acd8-916d2e3d0fa5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdawd (ID: 0f27042d-3329-4823-9fb7-bbf7455feb6c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawda (ID: 5b2b5170-839e-48e7-9363-c5749cb7cff1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddwadwadw (ID: dcad3d2a-859d-48a6-96ea-a1e0a9c5840b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawda (ID: 8d5a42ff-95fd-48a8-b128-5d9776ebe6de)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - safwef (ID: a68bf3e5-5d2f-436d-8d5a-0410d5c285d9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadaw (ID: 024247bf-9275-4b17-9615-4ee89f4529a3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test1000 (ID: 1c41e035-1e9c-4508-9095-8b85fd99b3ed)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - alo (ID: 9bd7ba35-3161-4175-b0a1-bab05aff119c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadasdadasd (ID: 853f9480-d66b-4dfa-985e-2a4d71339b88)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdadw (ID: 5abfa938-5fc3-4d16-b0da-fbbda5929320)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaaa (ID: 50c8de1a-75e0-49b1-b98a-b1eb195a7331)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadaws (ID: a829eda3-08b2-4d0d-a59d-e94f2c590635)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawd (ID: 77198cd6-f6e9-47d5-9195-b53da486c253)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwawa (ID: e8f21959-b887-4d17-b8bc-c21ca9c67bd2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaa (ID: e39ae71e-3f67-4511-9f30-5ecca1ab9a34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadwada (ID: 24de29f1-549b-46a3-81aa-1e50e914fb6c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaw (ID: b883ad5c-d027-4583-bd31-cf09b80448de)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a s s sd d  d d d d d d d d d (ID: 04d2ca7a-2895-4135-9fe9-bed8bf252b82)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwa (ID: 3b7f071f-4ae7-4fb1-a420-9df4a30a0577)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdawd (ID: c05f7e64-51ec-4c7b-9853-5c0792891dd0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadasdaw (ID: 61e12bd6-3ac2-4033-894d-78869890173f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - DWDAWA (ID: 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwadwad (ID: 483238da-c158-4139-b084-9d533f5718ac)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdaw (ID: feaef9f1-3244-4dec-acaf-22899d837df7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawwad (ID: e8bd1386-9e2c-4865-b9e2-3a58d5e22f82)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaa (ID: 8259edfd-51df-4a27-b0d6-47aa85fb2073)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwdwa (ID: cabe8ac7-596d-491c-91de-3b4ce8a66892)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pooeooooooooooooooo (ID: 70481211-c3b9-44a0-ba28-6940bcb26a34)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadaw (ID: 8bcf849e-3b9f-45ce-888d-ff3abe156a0c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdawd (ID: 26f52792-3e06-45a4-a84d-39ffa6585381)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: 75c4e2af-d0a7-4da7-9292-0cc6250de180)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwad (ID: 4cd60086-5feb-4f59-aa1a-d1989fc01c14)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - tee (ID: 18a45882-f713-4b73-9217-94a805ac35e0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadw (ID: ce1eec03-90dd-4e55-aeb9-e402e83f88a4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawa (ID: 100982b7-1713-47f8-b1b7-e9e66cdf706d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wDwdadww (ID: fa316ed1-964f-4935-977e-9902843af565)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwa (ID: c551b58b-33cc-47f2-b6b0-2dcf154b906e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdawdawdaw (ID: 398b6c99-8cb1-4ed1-8c38-08623d693883)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm (ID: e6568df3-4194-44d1-9846-44afadaebd6e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwawdwa (ID: 5b56da19-9d90-4027-b4d7-37f49dd40ac4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawda (ID: feea5a92-9db6-4071-9c56-053fae2e7c35)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawda (ID: 16ba766f-660b-446c-83ee-88fcaf32a446)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: fd030878-1735-4865-91ce-f39925b5c51e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwa (ID: 45971005-1752-45a4-80da-ae2ddff4aebf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ouggggggggggg (ID: f93c96c3-740c-4116-8e18-57737635fd63)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - jean paul (ID: 037d4124-446d-441d-8523-ac61548b293b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasdweas (ID: 9336d7bc-c90e-4ddd-b298-cbce4a56dd87)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadawda (ID: c029c77a-2c4f-4dcb-bcae-7b79b02671f8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwaaw (ID: a519cc83-44ed-4853-b0fa-101caa456766)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test3 (ID: 8aad57c3-16ed-4a95-9422-436154898639)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdadaw (ID: aa09191d-39e3-4375-b0b8-be26bce11118)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsfdfsfs (ID: 24c4f956-bc10-4cce-b33e-1c136f58e3df)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdadsa (ID: 1f0132cb-89fb-452b-bdfc-7a3141593568)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yesssssssssss (ID: 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawda (ID: bd55053d-02ed-4802-b2fc-121047bd4722)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasfa (ID: c73d558c-d848-49cd-956a-9196a7df24e3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwadwad (ID: 9b41ee08-47e3-4ec5-b69d-21011c15e31e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadawda (ID: 6415c670-1162-4e39-9be8-5d67e9332478)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwdaaa (ID: 7448d1e9-4f39-4475-adcf-e1478168e7f3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - afgee (ID: 7ca97c57-ea96-4c08-8e42-c6d5c004720c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawwda (ID: ef7bd278-fff3-44f9-8dd5-9f4ec7be1424)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawa (ID: d812caf2-0303-4df8-8e53-ebc00de73364)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssssssssssssssssssssssssssss (ID: ee5fafb3-4693-4286-bf63-5aa778375907)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdaawa (ID: 3ee54270-2c98-441f-8906-5a9238ed9a66)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwa (ID: 40af2c0b-19d3-47d2-a1ce-72eb435e53d3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwaaw (ID: ecdedce2-34d7-43e2-ae6c-397a4f759279)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdaa (ID: 00981050-1af7-4fe0-aa17-a4d83fdc07d8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssss (ID: 29172492-27b5-4802-884f-21ec817307f6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawa (ID: 0a6490c2-9ec7-4578-96c4-c05c0e4046d4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwa (ID: a65645a5-9915-46ab-a4e5-4a3eafa7aa26)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanus (ID: d40872af-b1f5-431d-b0f7-580fa07c67dc)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdadwa (ID: e720508e-64b6-4294-8179-d176cf6c39f1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pinis (ID: e40ab16d-fc22-4a3e-8c88-3f44a25383fa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwadfsdasd (ID: 975c2700-1680-4cc5-8acc-9af93162426d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdaw (ID: cc637ba9-3435-4ac1-8e37-aff6a2780fa4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - annus (ID: 272cef11-acb6-4ee0-88f2-3f1f3e3c249c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawda (ID: b53ee7aa-7d5f-4052-af2b-43114c79cef2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwad (ID: 678bd7c2-b672-4c9f-985d-f978d3584ff2)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwadwadaw (ID: 976bc1ed-8f12-4c33-aac7-6bc69cd19558)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - 00000000000000000000000000000000 (ID: 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: a2f28896-5544-4b5d-b50c-fbd99a2e77b5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasasayaaaaaaaaaaaaaaaaaaaaa (ID: 264224c1-6867-4502-8b49-7aaa42aef79f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaw (ID: ea59a2f8-3ef2-41d0-be76-e78be54ee805)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessirk (ID: d96582db-2363-4cdd-ad68-fd69840e7139)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdas (ID: ffa7b015-7803-48af-871a-e31eec921abf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawaw (ID: d7c3103f-c296-4e38-8900-c087f6d91d3b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdawdwad (ID: ac10c935-e61e-47c5-8e1c-ec5d3a286daf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwwdwa (ID: f51f5d15-e98a-422e-805d-8785df82bcb4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadw (ID: 4995ef19-314c-4a36-a400-6bf82e748d3a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawd (ID: c3e140c2-8b05-4b14-997f-863ed53c000f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsafasfaf (ID: f247d133-8e78-400d-b326-a382fc76969f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wda (ID: cc60a66a-494b-482f-9eab-410b179ae619)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwad (ID: 13b44232-8023-4bce-bcbc-186ad6f9070b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testtttttttttttttttttttttttttttt (ID: 2f388ab9-8161-4f4e-8ffb-1a65afe5449a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadawdad (ID: 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: bd36e320-c182-46a4-ba60-cc3ba4a0749c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadaw (ID: 1cb5ad85-59b9-4095-825f-1364ef6d291d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - diuashduihawuihduia (ID: 1306a6ad-3ea4-4106-b4ab-09d645770558)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsafasfs (ID: 0f5b2980-3acc-4024-b5b4-19450a419fdf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 62796a47-945e-4936-9a95-4fa450ed07d6)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasad (ID: 16547467-d690-417e-bce9-4cd58a25c298)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawaad (ID: 0c39f35c-a615-4550-ab9f-e295f8be70cb)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwawdwa (ID: f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdaa (ID: a6007f69-3f95-4ecd-acde-d16d6ad62035)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsadwerwffsdfssssss (ID: af66689a-c04b-4249-a424-a710376856c5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwa (ID: 031efbcf-d63a-42ae-93f6-595fa590e61b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawaw (ID: f7b38e66-7063-48e5-b35a-48877a960372)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaa (ID: a6dd2c36-80bb-4c61-8ea2-865ea919fabf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daasa (ID: 00000cbc-9402-4ef1-b2e3-ba097c34fdfa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: 0256ccaa-f6f6-478b-bb23-0ebce2fcb993)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwd (ID: 59be9116-4344-48dc-a300-fd54b50cb51f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwaa (ID: 9b47b464-ef47-4123-8afe-b5da2a019d1b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaa (ID: 9e221a54-f864-4c17-9f6b-1d3b7f8350ad)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwada (ID: e1c23312-3859-40b2-ba18-624bd0d595c3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwawda (ID: 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fDAF (ID: 809bbcd3-c153-461e-a399-1ad841c4260c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdaw (ID: 42e9b17d-07df-4fba-bdd2-be676c80274a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdw (ID: aa2110b5-ddd6-4e2d-b17d-765dd51878c7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsaaaaaaaaaaaa (ID: 2442bf9f-8632-4327-b68a-78aa57b420cf)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 47668211-a0c8-453a-8658-c35c8acf9cdd)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa33333 (ID: b1ecfb16-e7c9-4919-b2b8-370550252426)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadaw (ID: aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddawdwa (ID: 23aa3f82-2714-4521-8789-be2b00331013)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawda (ID: d84f573a-932a-4af7-9095-719939a07fa4)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdaw (ID: 358a13f3-a1db-4741-8235-71300fd72993)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdada (ID: 75c433cf-7bcb-43eb-8d42-c782aa793f38)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwad (ID: f69a3537-b316-401e-8412-303a459930be)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawadasd (ID: ae0e56ab-942d-43ab-8a30-7c16be4a98fe)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdfdsdfa3343224324 (ID: 21638641-943d-4c4b-a075-f3abcc46797f)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 778bd3d6-bc14-459b-a87a-17869d9e9e04)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadasdasd (ID: 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwad (ID: 2adf158d-d3bf-4038-8e49-f53541fa73e8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawd (ID: 253734e7-c9f5-4d35-bcd6-302e42fc0ea8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing (ID: 079df915-52f6-4e8c-b5c3-54d04e586c07)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawdadawd (ID: 732435c6-5e90-41c9-a5a4-207559f9bc51)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadawdawd (ID: 067f1e0a-5eef-42cd-981d-65a1ef04392b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawwda (ID: 3793b743-654c-46d4-8b68-dcadb593ceda)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaawwwwwwwwwwwww (ID: 67641bc2-d6a5-400b-b469-50ff5df29aa9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwaa (ID: 1b8c6372-517b-4aa5-bf37-e0b10c14897a)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawa (ID: 90939159-fabb-462d-9d92-3105cce322e1)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdw (ID: afa43070-a3ac-4134-841f-efc29f43c218)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sfdsfetdfcfvcbbfghfhfhfdgfgrgf (ID: de8544a5-e176-4038-b699-419018a1530d)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaw (ID: 0e4d4772-3d89-453c-ba71-2426e168ef65)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ddddddddddddddddddddddddddddddd (ID: 01ace668-ce82-42b3-8f7c-c9a3219d4967)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing perm (ID: 25fd387c-35b2-43b1-9df4-95f0149c62c9)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sad (ID: 7331902b-ddbb-498e-b365-71d88ae42ca5)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daw (ID: 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - oui (ID: f0806c7c-7cb3-4f6a-9447-5d782af01932)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadwa (ID: f85bd8b2-1374-424a-89b0-2625bc473bd8)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadw (ID: 4a7dbb6e-8289-4274-9a11-25a61493c128)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawddaw (ID: a5d6a06b-a9c7-4067-be5e-a0209e201fa0)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawd (ID: 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaaaaaa (ID: 08db5308-e82e-4ee5-9f66-1f66a5da672b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsadasdasd (ID: c2569049-fcdf-4278-ac80-d1cf35c645db)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwdadw (ID: a06dce52-acae-4249-bc9b-eb3840ee3e7e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawdawddaw (ID: 9ff3db28-a0d5-45cd-9b59-fd34d9640724)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 8a568eb9-0e15-4e8f-8185-96874fe6477e)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fuk (ID: 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - rrew (ID: 2f9a5448-417b-4f32-8023-44c71886c60c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawddwa (ID: d2fa02b5-eea0-495a-949c-d32c8e171be7)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdadw (ID: 612ff681-0fbe-42d1-b927-d4b9c12a5982)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwaad (ID: 9ee99cde-9bb9-4d9d-83ee-df2342551b7c)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdadwadsa (ID: 116028e9-21cf-435b-bedd-3745d285c7fa)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdaw (ID: 31d13c67-f565-4bd0-9c4d-a9ab620a5913)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwadwad (ID: f67d2e01-39e2-4cb0-8180-7dc6c1d78342)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: b51e7ecf-d238-48e7-8624-9540bf816a14)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dasdawdwa (ID: 2d685456-5807-4590-b4ec-2648a069959b)
[15:17:20] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdad (ID: 220f69b7-1a72-4180-9533-1cb20000f234)
[15:17:20] [Render thread/INFO] (pokecobbleclaim) CLIENT: Updated claim count for town wadaw from 0/4 to 0/4
[15:17:23] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received town invite packet
[15:17:23] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received invitation from town: wadaw (aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:23] [Render thread/INFO] (pokecobbleclaim) Setting pending invite for town: wadaw
[15:17:23] [Render thread/INFO] (pokecobbleclaim) Received invitation from town: wadaw
[15:17:23] [Render thread/INFO] (pokecobbleclaim) Added town invite notification for: wadaw - showed phone with overlay
[15:17:23] [Render thread/INFO] (Minecraft) [System] [CHAT] You have been invited to join wadaw!
[15:17:23] [Render thread/INFO] (Minecraft) [System] [CHAT] You have been invited to join wadaw!
[15:17:23] [Render thread/INFO] (pokecobbleclaim) Screen is open, not showing phone overlay
[15:17:23] [Render thread/INFO] (pokecobbleclaim) HUD Render - Phone visible: false, Overlay visible: false, Has notifications: true
[15:17:25] [Render thread/INFO] (pokecobbleclaim) Town key (T) was pressed
[15:17:25] [Render thread/INFO] (pokecobbleclaim) Town key pressed, opening town screen
[15:17:25] [Render thread/INFO] (pokecobbleclaim) Opening town screen
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town player update for town aceee40b-077b-421b-83ee-36e196d28fc3, firing player update events for 4 players
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 681f539b-8bb8-3f85-85e5-a2945f6c6539 with rank Mayor
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 11e2b7aa-6bda-3ca8-b793-391234896312 with rank Admin Viewer
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player 34c6f761-a0d4-3d62-b5ee-df15e4530215 with rank Resident
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Firing player update event for player aed5efd4-551b-3965-bc28-ae21aa072a66 with rank Resident
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: handleTownListResponse called - packet received!
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town list update with 213 towns
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwa (ID: bc555394-bbf0-475a-95a1-0b17c9d36c0f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwadawa (ID: 853b9a47-40bf-4521-b523-868339a8a0ee)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadw (ID: 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawdaw (ID: 6a314cd9-189f-4b6e-864d-81aa21f6af0f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwad (ID: 6c57a9a0-6448-452d-a814-b8d9040e6043)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadw (ID: 20ede486-11b9-4d1f-b8cc-202ccc0baa34)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdssadasd (ID: d72feaa8-f4c1-4532-a294-8e00634a0dbf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwaaw (ID: e91a1bc1-2579-49dc-802b-cf19d448d911)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadwadaw (ID: 906f24a7-d1ca-455c-aca0-dbc94dbed0a8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a a a a aa a  a a a a a aa (ID: 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adawdawd (ID: dcf00911-67f1-4159-9367-6c4c81c89f88)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaadasdasdad (ID: 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdrewrghr (ID: 6e99a3dd-33fe-4563-981f-05d4583d920a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadw (ID: a39c6ace-b46a-47d1-bd08-5fbfc078aa17)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adjsa (ID: be0d82f4-3880-4b9e-a3eb-5e747ebaeef3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadwa (ID: 669d5f05-6ef9-4e7f-a17b-ca50fc162411)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawda (ID: 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawd (ID: d9786d4d-fc23-4454-af9f-f9adafa9bdec)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waaaaaaaaaaaaptssssssssssss (ID: 464f7431-c572-4387-8f91-4edb6613f7e4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawd (ID: 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwaa (ID: f10db762-14e2-4e7e-aa38-41b601cda088)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdwadwa (ID: 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ougaga (ID: 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testicule (ID: b0d2f8dd-3094-42ab-bee1-07f2579bb4ff)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwdada (ID: 9a5193c2-4fea-49c7-a4d6-d8709e7de329)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadadwdawd (ID: 9c1b8f4a-051c-4a69-9c92-5ac16b610544)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadasda (ID: 17af5a11-bd38-41e4-a574-70779fdb21ee)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwada (ID: 78f54b6b-e425-4d18-9933-469118c7019d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwa (ID: 9248516c-92ac-4cd0-8cdf-1dc330367029)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessir (ID: f0e54b62-4630-4c36-b388-f8caa0ee9e71)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadaw (ID: 88840423-accd-4f2e-8fca-d372b807cda7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwawd (ID: 032985e0-551d-422f-9893-e791ea418ce6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdw (ID: 741e6313-004f-46bb-ba8c-cee7a6b12f43)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdwadawda (ID: 4d60dd17-44c1-4e97-9888-32e563b56199)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanusima (ID: 05e3661f-4790-4fc7-8368-85bc77f1aeef)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - addawdadwad (ID: 733810c2-ee73-4c36-8f01-f32508c7b33f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaa (ID: 6f823d06-f53d-478b-b50e-766154019f39)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasa (ID: 27907c34-f576-4d02-b00d-ae74912e0ce2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdwad (ID: 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ticsssss (ID: 0250cce5-1df9-4fb2-9cce-04b98f710590)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdad (ID: 08fda338-bbea-426e-acd8-916d2e3d0fa5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdawd (ID: 0f27042d-3329-4823-9fb7-bbf7455feb6c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawda (ID: 5b2b5170-839e-48e7-9363-c5749cb7cff1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddwadwadw (ID: dcad3d2a-859d-48a6-96ea-a1e0a9c5840b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawda (ID: 8d5a42ff-95fd-48a8-b128-5d9776ebe6de)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - safwef (ID: a68bf3e5-5d2f-436d-8d5a-0410d5c285d9)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadaw (ID: 024247bf-9275-4b17-9615-4ee89f4529a3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test1000 (ID: 1c41e035-1e9c-4508-9095-8b85fd99b3ed)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - alo (ID: 9bd7ba35-3161-4175-b0a1-bab05aff119c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadasdadasd (ID: 853f9480-d66b-4dfa-985e-2a4d71339b88)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdadw (ID: 5abfa938-5fc3-4d16-b0da-fbbda5929320)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaaa (ID: 50c8de1a-75e0-49b1-b98a-b1eb195a7331)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadaws (ID: a829eda3-08b2-4d0d-a59d-e94f2c590635)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawd (ID: 77198cd6-f6e9-47d5-9195-b53da486c253)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwawa (ID: e8f21959-b887-4d17-b8bc-c21ca9c67bd2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaa (ID: e39ae71e-3f67-4511-9f30-5ecca1ab9a34)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadwada (ID: 24de29f1-549b-46a3-81aa-1e50e914fb6c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaw (ID: b883ad5c-d027-4583-bd31-cf09b80448de)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a s s sd d  d d d d d d d d d (ID: 04d2ca7a-2895-4135-9fe9-bed8bf252b82)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwa (ID: 3b7f071f-4ae7-4fb1-a420-9df4a30a0577)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdawd (ID: c05f7e64-51ec-4c7b-9853-5c0792891dd0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadasdaw (ID: 61e12bd6-3ac2-4033-894d-78869890173f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - DWDAWA (ID: 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwadwad (ID: 483238da-c158-4139-b084-9d533f5718ac)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdaw (ID: feaef9f1-3244-4dec-acaf-22899d837df7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawwad (ID: e8bd1386-9e2c-4865-b9e2-3a58d5e22f82)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaa (ID: 8259edfd-51df-4a27-b0d6-47aa85fb2073)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwdwa (ID: cabe8ac7-596d-491c-91de-3b4ce8a66892)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pooeooooooooooooooo (ID: 70481211-c3b9-44a0-ba28-6940bcb26a34)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadaw (ID: 8bcf849e-3b9f-45ce-888d-ff3abe156a0c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdawd (ID: 26f52792-3e06-45a4-a84d-39ffa6585381)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: 75c4e2af-d0a7-4da7-9292-0cc6250de180)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwad (ID: 4cd60086-5feb-4f59-aa1a-d1989fc01c14)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - tee (ID: 18a45882-f713-4b73-9217-94a805ac35e0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadw (ID: ce1eec03-90dd-4e55-aeb9-e402e83f88a4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawa (ID: 100982b7-1713-47f8-b1b7-e9e66cdf706d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wDwdadww (ID: fa316ed1-964f-4935-977e-9902843af565)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwa (ID: c551b58b-33cc-47f2-b6b0-2dcf154b906e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdawdawdaw (ID: 398b6c99-8cb1-4ed1-8c38-08623d693883)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm (ID: e6568df3-4194-44d1-9846-44afadaebd6e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwawdwa (ID: 5b56da19-9d90-4027-b4d7-37f49dd40ac4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawda (ID: feea5a92-9db6-4071-9c56-053fae2e7c35)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawda (ID: 16ba766f-660b-446c-83ee-88fcaf32a446)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: fd030878-1735-4865-91ce-f39925b5c51e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwa (ID: 45971005-1752-45a4-80da-ae2ddff4aebf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ouggggggggggg (ID: f93c96c3-740c-4116-8e18-57737635fd63)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - jean paul (ID: 037d4124-446d-441d-8523-ac61548b293b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasdweas (ID: 9336d7bc-c90e-4ddd-b298-cbce4a56dd87)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadawda (ID: c029c77a-2c4f-4dcb-bcae-7b79b02671f8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwaaw (ID: a519cc83-44ed-4853-b0fa-101caa456766)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test3 (ID: 8aad57c3-16ed-4a95-9422-436154898639)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdadaw (ID: aa09191d-39e3-4375-b0b8-be26bce11118)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsfdfsfs (ID: 24c4f956-bc10-4cce-b33e-1c136f58e3df)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdadsa (ID: 1f0132cb-89fb-452b-bdfc-7a3141593568)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yesssssssssss (ID: 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawda (ID: bd55053d-02ed-4802-b2fc-121047bd4722)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasfa (ID: c73d558c-d848-49cd-956a-9196a7df24e3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwadwad (ID: 9b41ee08-47e3-4ec5-b69d-21011c15e31e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadawda (ID: 6415c670-1162-4e39-9be8-5d67e9332478)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwdaaa (ID: 7448d1e9-4f39-4475-adcf-e1478168e7f3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - afgee (ID: 7ca97c57-ea96-4c08-8e42-c6d5c004720c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawwda (ID: ef7bd278-fff3-44f9-8dd5-9f4ec7be1424)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawa (ID: d812caf2-0303-4df8-8e53-ebc00de73364)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssssssssssssssssssssssssssss (ID: ee5fafb3-4693-4286-bf63-5aa778375907)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdaawa (ID: 3ee54270-2c98-441f-8906-5a9238ed9a66)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwa (ID: 40af2c0b-19d3-47d2-a1ce-72eb435e53d3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwaaw (ID: ecdedce2-34d7-43e2-ae6c-397a4f759279)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdaa (ID: 00981050-1af7-4fe0-aa17-a4d83fdc07d8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssss (ID: 29172492-27b5-4802-884f-21ec817307f6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawa (ID: 0a6490c2-9ec7-4578-96c4-c05c0e4046d4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwa (ID: a65645a5-9915-46ab-a4e5-4a3eafa7aa26)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanus (ID: d40872af-b1f5-431d-b0f7-580fa07c67dc)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdadwa (ID: e720508e-64b6-4294-8179-d176cf6c39f1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pinis (ID: e40ab16d-fc22-4a3e-8c88-3f44a25383fa)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwadfsdasd (ID: 975c2700-1680-4cc5-8acc-9af93162426d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdaw (ID: cc637ba9-3435-4ac1-8e37-aff6a2780fa4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - annus (ID: 272cef11-acb6-4ee0-88f2-3f1f3e3c249c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawda (ID: b53ee7aa-7d5f-4052-af2b-43114c79cef2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwad (ID: 678bd7c2-b672-4c9f-985d-f978d3584ff2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwadwadaw (ID: 976bc1ed-8f12-4c33-aac7-6bc69cd19558)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - 00000000000000000000000000000000 (ID: 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: a2f28896-5544-4b5d-b50c-fbd99a2e77b5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasasayaaaaaaaaaaaaaaaaaaaaa (ID: 264224c1-6867-4502-8b49-7aaa42aef79f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaw (ID: ea59a2f8-3ef2-41d0-be76-e78be54ee805)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessirk (ID: d96582db-2363-4cdd-ad68-fd69840e7139)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdas (ID: ffa7b015-7803-48af-871a-e31eec921abf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawaw (ID: d7c3103f-c296-4e38-8900-c087f6d91d3b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdawdwad (ID: ac10c935-e61e-47c5-8e1c-ec5d3a286daf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwwdwa (ID: f51f5d15-e98a-422e-805d-8785df82bcb4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadw (ID: 4995ef19-314c-4a36-a400-6bf82e748d3a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawd (ID: c3e140c2-8b05-4b14-997f-863ed53c000f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsafasfaf (ID: f247d133-8e78-400d-b326-a382fc76969f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wda (ID: cc60a66a-494b-482f-9eab-410b179ae619)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwad (ID: 13b44232-8023-4bce-bcbc-186ad6f9070b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testtttttttttttttttttttttttttttt (ID: 2f388ab9-8161-4f4e-8ffb-1a65afe5449a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadawdad (ID: 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: bd36e320-c182-46a4-ba60-cc3ba4a0749c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadaw (ID: 1cb5ad85-59b9-4095-825f-1364ef6d291d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - diuashduihawuihduia (ID: 1306a6ad-3ea4-4106-b4ab-09d645770558)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsafasfs (ID: 0f5b2980-3acc-4024-b5b4-19450a419fdf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 62796a47-945e-4936-9a95-4fa450ed07d6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasad (ID: 16547467-d690-417e-bce9-4cd58a25c298)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawaad (ID: 0c39f35c-a615-4550-ab9f-e295f8be70cb)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwawdwa (ID: f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdaa (ID: a6007f69-3f95-4ecd-acde-d16d6ad62035)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsadwerwffsdfssssss (ID: af66689a-c04b-4249-a424-a710376856c5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwa (ID: 031efbcf-d63a-42ae-93f6-595fa590e61b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawaw (ID: f7b38e66-7063-48e5-b35a-48877a960372)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaa (ID: a6dd2c36-80bb-4c61-8ea2-865ea919fabf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daasa (ID: 00000cbc-9402-4ef1-b2e3-ba097c34fdfa)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: 0256ccaa-f6f6-478b-bb23-0ebce2fcb993)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwd (ID: 59be9116-4344-48dc-a300-fd54b50cb51f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwaa (ID: 9b47b464-ef47-4123-8afe-b5da2a019d1b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaa (ID: 9e221a54-f864-4c17-9f6b-1d3b7f8350ad)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwada (ID: e1c23312-3859-40b2-ba18-624bd0d595c3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwawda (ID: 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fDAF (ID: 809bbcd3-c153-461e-a399-1ad841c4260c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdaw (ID: 42e9b17d-07df-4fba-bdd2-be676c80274a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdw (ID: aa2110b5-ddd6-4e2d-b17d-765dd51878c7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsaaaaaaaaaaaa (ID: 2442bf9f-8632-4327-b68a-78aa57b420cf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 47668211-a0c8-453a-8658-c35c8acf9cdd)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa33333 (ID: b1ecfb16-e7c9-4919-b2b8-370550252426)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadaw (ID: aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddawdwa (ID: 23aa3f82-2714-4521-8789-be2b00331013)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawda (ID: d84f573a-932a-4af7-9095-719939a07fa4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdaw (ID: 358a13f3-a1db-4741-8235-71300fd72993)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdada (ID: 75c433cf-7bcb-43eb-8d42-c782aa793f38)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwad (ID: f69a3537-b316-401e-8412-303a459930be)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawadasd (ID: ae0e56ab-942d-43ab-8a30-7c16be4a98fe)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdfdsdfa3343224324 (ID: 21638641-943d-4c4b-a075-f3abcc46797f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 778bd3d6-bc14-459b-a87a-17869d9e9e04)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadasdasd (ID: 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwad (ID: 2adf158d-d3bf-4038-8e49-f53541fa73e8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawd (ID: 253734e7-c9f5-4d35-bcd6-302e42fc0ea8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing (ID: 079df915-52f6-4e8c-b5c3-54d04e586c07)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawdadawd (ID: 732435c6-5e90-41c9-a5a4-207559f9bc51)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadawdawd (ID: 067f1e0a-5eef-42cd-981d-65a1ef04392b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawwda (ID: 3793b743-654c-46d4-8b68-dcadb593ceda)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaawwwwwwwwwwwww (ID: 67641bc2-d6a5-400b-b469-50ff5df29aa9)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwaa (ID: 1b8c6372-517b-4aa5-bf37-e0b10c14897a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawa (ID: 90939159-fabb-462d-9d92-3105cce322e1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdw (ID: afa43070-a3ac-4134-841f-efc29f43c218)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sfdsfetdfcfvcbbfghfhfhfdgfgrgf (ID: de8544a5-e176-4038-b699-419018a1530d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaw (ID: 0e4d4772-3d89-453c-ba71-2426e168ef65)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ddddddddddddddddddddddddddddddd (ID: 01ace668-ce82-42b3-8f7c-c9a3219d4967)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing perm (ID: 25fd387c-35b2-43b1-9df4-95f0149c62c9)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sad (ID: 7331902b-ddbb-498e-b365-71d88ae42ca5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daw (ID: 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - oui (ID: f0806c7c-7cb3-4f6a-9447-5d782af01932)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadwa (ID: f85bd8b2-1374-424a-89b0-2625bc473bd8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadw (ID: 4a7dbb6e-8289-4274-9a11-25a61493c128)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawddaw (ID: a5d6a06b-a9c7-4067-be5e-a0209e201fa0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawd (ID: 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaaaaaa (ID: 08db5308-e82e-4ee5-9f66-1f66a5da672b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsadasdasd (ID: c2569049-fcdf-4278-ac80-d1cf35c645db)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwdadw (ID: a06dce52-acae-4249-bc9b-eb3840ee3e7e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawdawddaw (ID: 9ff3db28-a0d5-45cd-9b59-fd34d9640724)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 8a568eb9-0e15-4e8f-8185-96874fe6477e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fuk (ID: 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - rrew (ID: 2f9a5448-417b-4f32-8023-44c71886c60c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawddwa (ID: d2fa02b5-eea0-495a-949c-d32c8e171be7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdadw (ID: 612ff681-0fbe-42d1-b927-d4b9c12a5982)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwaad (ID: 9ee99cde-9bb9-4d9d-83ee-df2342551b7c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdadwadsa (ID: 116028e9-21cf-435b-bedd-3745d285c7fa)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdaw (ID: 31d13c67-f565-4bd0-9c4d-a9ab620a5913)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwadwad (ID: f67d2e01-39e2-4cb0-8180-7dc6c1d78342)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: b51e7ecf-d238-48e7-8624-9540bf816a14)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dasdawdwa (ID: 2d685456-5807-4590-b4ec-2648a069959b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdad (ID: 220f69b7-1a72-4180-9533-1cb20000f234)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: handleTownListResponse called - packet received!
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT: Received town list update with 213 towns
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwa (ID: bc555394-bbf0-475a-95a1-0b17c9d36c0f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwadawa (ID: 853b9a47-40bf-4521-b523-868339a8a0ee)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadw (ID: 5c6fbf06-c2f9-4b95-a9b9-8710e5c0b169)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawdaw (ID: 6a314cd9-189f-4b6e-864d-81aa21f6af0f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwad (ID: 6c57a9a0-6448-452d-a814-b8d9040e6043)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadw (ID: 20ede486-11b9-4d1f-b8cc-202ccc0baa34)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdssadasd (ID: d72feaa8-f4c1-4532-a294-8e00634a0dbf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwaaw (ID: e91a1bc1-2579-49dc-802b-cf19d448d911)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadwadaw (ID: 906f24a7-d1ca-455c-aca0-dbc94dbed0a8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a a a a aa a  a a a a a aa (ID: 3b9c6d73-6aae-4c1f-91b8-fb4a5d2d6da3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adawdawd (ID: dcf00911-67f1-4159-9367-6c4c81c89f88)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaadasdasdad (ID: 3fd36593-6f64-4fbc-a1be-2b34d2c4bef2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdrewrghr (ID: 6e99a3dd-33fe-4563-981f-05d4583d920a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadw (ID: a39c6ace-b46a-47d1-bd08-5fbfc078aa17)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adjsa (ID: be0d82f4-3880-4b9e-a3eb-5e747ebaeef3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadwa (ID: 669d5f05-6ef9-4e7f-a17b-ca50fc162411)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawda (ID: 3b117137-d22a-4114-ae3a-dc9d4f6b3ee0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawd (ID: d9786d4d-fc23-4454-af9f-f9adafa9bdec)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waaaaaaaaaaaaptssssssssssss (ID: 464f7431-c572-4387-8f91-4edb6613f7e4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawd (ID: 5901a8a9-640a-41fa-b58a-f5e6b7c8bfca)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwaa (ID: f10db762-14e2-4e7e-aa38-41b601cda088)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdwadwa (ID: 3b838542-b2f8-49bb-80e5-ca9ee0c4bf02)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ougaga (ID: 9ce0b73f-64c9-4d80-8e1a-a25b37eed57a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testicule (ID: b0d2f8dd-3094-42ab-bee1-07f2579bb4ff)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwdada (ID: 9a5193c2-4fea-49c7-a4d6-d8709e7de329)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadadwdawd (ID: 9c1b8f4a-051c-4a69-9c92-5ac16b610544)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadasda (ID: 17af5a11-bd38-41e4-a574-70779fdb21ee)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwada (ID: 78f54b6b-e425-4d18-9933-469118c7019d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwa (ID: 9248516c-92ac-4cd0-8cdf-1dc330367029)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessir (ID: f0e54b62-4630-4c36-b388-f8caa0ee9e71)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadaw (ID: 88840423-accd-4f2e-8fca-d372b807cda7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: c279a2aa-6379-4f5a-9ec5-bce65fa5d52a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadwawd (ID: 032985e0-551d-422f-9893-e791ea418ce6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdw (ID: 741e6313-004f-46bb-ba8c-cee7a6b12f43)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdwadawda (ID: 4d60dd17-44c1-4e97-9888-32e563b56199)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanusima (ID: 05e3661f-4790-4fc7-8368-85bc77f1aeef)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - addawdadwad (ID: 733810c2-ee73-4c36-8f01-f32508c7b33f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaa (ID: 6f823d06-f53d-478b-b50e-766154019f39)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasa (ID: 27907c34-f576-4d02-b00d-ae74912e0ce2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdwad (ID: 8606a6e9-5b0b-4cf7-b22a-bf783789c5ae)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ticsssss (ID: 0250cce5-1df9-4fb2-9cce-04b98f710590)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - saddawdad (ID: 08fda338-bbea-426e-acd8-916d2e3d0fa5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdawd (ID: 0f27042d-3329-4823-9fb7-bbf7455feb6c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawda (ID: 5b2b5170-839e-48e7-9363-c5749cb7cff1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddwadwadw (ID: dcad3d2a-859d-48a6-96ea-a1e0a9c5840b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawda (ID: 8d5a42ff-95fd-48a8-b128-5d9776ebe6de)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - safwef (ID: a68bf3e5-5d2f-436d-8d5a-0410d5c285d9)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadaw (ID: 024247bf-9275-4b17-9615-4ee89f4529a3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test1000 (ID: 1c41e035-1e9c-4508-9095-8b85fd99b3ed)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - alo (ID: 9bd7ba35-3161-4175-b0a1-bab05aff119c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadasdadasd (ID: 853f9480-d66b-4dfa-985e-2a4d71339b88)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdadw (ID: 5abfa938-5fc3-4d16-b0da-fbbda5929320)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaaa (ID: 50c8de1a-75e0-49b1-b98a-b1eb195a7331)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwadaws (ID: a829eda3-08b2-4d0d-a59d-e94f2c590635)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawd (ID: 77198cd6-f6e9-47d5-9195-b53da486c253)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwawa (ID: e8f21959-b887-4d17-b8bc-c21ca9c67bd2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaa (ID: e39ae71e-3f67-4511-9f30-5ecca1ab9a34)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadwada (ID: 24de29f1-549b-46a3-81aa-1e50e914fb6c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaw (ID: b883ad5c-d027-4583-bd31-cf09b80448de)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - a a s s sd d  d d d d d d d d d (ID: 04d2ca7a-2895-4135-9fe9-bed8bf252b82)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwa (ID: 3b7f071f-4ae7-4fb1-a420-9df4a30a0577)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwdawd (ID: c05f7e64-51ec-4c7b-9853-5c0792891dd0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadasdaw (ID: 61e12bd6-3ac2-4033-894d-78869890173f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - DWDAWA (ID: 633fe6b2-3bb2-43a0-8e9e-c60f950b5ea1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwadwad (ID: 483238da-c158-4139-b084-9d533f5718ac)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdaw (ID: feaef9f1-3244-4dec-acaf-22899d837df7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawwad (ID: e8bd1386-9e2c-4865-b9e2-3a58d5e22f82)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaa (ID: 8259edfd-51df-4a27-b0d6-47aa85fb2073)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwdwa (ID: cabe8ac7-596d-491c-91de-3b4ce8a66892)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pooeooooooooooooooo (ID: 70481211-c3b9-44a0-ba28-6940bcb26a34)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwadaw (ID: 8bcf849e-3b9f-45ce-888d-ff3abe156a0c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdawd (ID: 26f52792-3e06-45a4-a84d-39ffa6585381)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test (ID: 75c4e2af-d0a7-4da7-9292-0cc6250de180)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwad (ID: 4cd60086-5feb-4f59-aa1a-d1989fc01c14)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - tee (ID: 18a45882-f713-4b73-9217-94a805ac35e0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadw (ID: ce1eec03-90dd-4e55-aeb9-e402e83f88a4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawa (ID: 100982b7-1713-47f8-b1b7-e9e66cdf706d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wDwdadww (ID: fa316ed1-964f-4935-977e-9902843af565)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwa (ID: c551b58b-33cc-47f2-b6b0-2dcf154b906e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdawdawdaw (ID: 398b6c99-8cb1-4ed1-8c38-08623d693883)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm (ID: e6568df3-4194-44d1-9846-44afadaebd6e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwawdwa (ID: 5b56da19-9d90-4027-b4d7-37f49dd40ac4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawda (ID: feea5a92-9db6-4071-9c56-053fae2e7c35)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawda (ID: 16ba766f-660b-446c-83ee-88fcaf32a446)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: fd030878-1735-4865-91ce-f39925b5c51e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwa (ID: 45971005-1752-45a4-80da-ae2ddff4aebf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ouggggggggggg (ID: f93c96c3-740c-4116-8e18-57737635fd63)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - jean paul (ID: 037d4124-446d-441d-8523-ac61548b293b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasdweas (ID: 9336d7bc-c90e-4ddd-b298-cbce4a56dd87)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwadawda (ID: c029c77a-2c4f-4dcb-bcae-7b79b02671f8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwaaw (ID: a519cc83-44ed-4853-b0fa-101caa456766)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - test3 (ID: 8aad57c3-16ed-4a95-9422-436154898639)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdadaw (ID: aa09191d-39e3-4375-b0b8-be26bce11118)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsfdfsfs (ID: 24c4f956-bc10-4cce-b33e-1c136f58e3df)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdasdadsa (ID: 1f0132cb-89fb-452b-bdfc-7a3141593568)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yesssssssssss (ID: 31bf891c-742d-4c4c-8af4-ec6b5fd9ff6e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawda (ID: bd55053d-02ed-4802-b2fc-121047bd4722)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasfa (ID: c73d558c-d848-49cd-956a-9196a7df24e3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawdwadwad (ID: 9b41ee08-47e3-4ec5-b69d-21011c15e31e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadawda (ID: 6415c670-1162-4e39-9be8-5d67e9332478)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: d42f4ef4-ff82-4347-a77c-d322040ff0b2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwdaaa (ID: 7448d1e9-4f39-4475-adcf-e1478168e7f3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - afgee (ID: 7ca97c57-ea96-4c08-8e42-c6d5c004720c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa (ID: 6a557cc3-fb5f-427a-a926-bafe2d3a0a56)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawwda (ID: ef7bd278-fff3-44f9-8dd5-9f4ec7be1424)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawa (ID: d812caf2-0303-4df8-8e53-ebc00de73364)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssssssssssssssssssssssssssss (ID: ee5fafb3-4693-4286-bf63-5aa778375907)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdaawa (ID: 3ee54270-2c98-441f-8906-5a9238ed9a66)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwa (ID: 40af2c0b-19d3-47d2-a1ce-72eb435e53d3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwaaw (ID: ecdedce2-34d7-43e2-ae6c-397a4f759279)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawdaa (ID: 00981050-1af7-4fe0-aa17-a4d83fdc07d8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessssss (ID: 29172492-27b5-4802-884f-21ec817307f6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawa (ID: 0a6490c2-9ec7-4578-96c4-c05c0e4046d4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdwa (ID: a65645a5-9915-46ab-a4e5-4a3eafa7aa26)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - analanus (ID: d40872af-b1f5-431d-b0f7-580fa07c67dc)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdadwa (ID: e720508e-64b6-4294-8179-d176cf6c39f1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - pinis (ID: e40ab16d-fc22-4a3e-8c88-3f44a25383fa)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - adwadfsdasd (ID: 975c2700-1680-4cc5-8acc-9af93162426d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdaw (ID: cc637ba9-3435-4ac1-8e37-aff6a2780fa4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - annus (ID: 272cef11-acb6-4ee0-88f2-3f1f3e3c249c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdawda (ID: b53ee7aa-7d5f-4052-af2b-43114c79cef2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwad (ID: 678bd7c2-b672-4c9f-985d-f978d3584ff2)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwadwadaw (ID: 976bc1ed-8f12-4c33-aac7-6bc69cd19558)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - 00000000000000000000000000000000 (ID: 9ff3cdf7-cd8c-4b3e-aa7b-6bc80c9176a6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: a2f28896-5544-4b5d-b50c-fbd99a2e77b5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sasasayaaaaaaaaaaaaaaaaaaaaa (ID: 264224c1-6867-4502-8b49-7aaa42aef79f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadaw (ID: ea59a2f8-3ef2-41d0-be76-e78be54ee805)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - yessirk (ID: d96582db-2363-4cdd-ad68-fd69840e7139)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdas (ID: ffa7b015-7803-48af-871a-e31eec921abf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawdawaw (ID: d7c3103f-c296-4e38-8900-c087f6d91d3b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdawdwad (ID: ac10c935-e61e-47c5-8e1c-ec5d3a286daf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwwdwa (ID: f51f5d15-e98a-422e-805d-8785df82bcb4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadw (ID: 4995ef19-314c-4a36-a400-6bf82e748d3a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadawd (ID: c3e140c2-8b05-4b14-997f-863ed53c000f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsafasfaf (ID: f247d133-8e78-400d-b326-a382fc76969f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wda (ID: cc60a66a-494b-482f-9eab-410b179ae619)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadadwad (ID: 13b44232-8023-4bce-bcbc-186ad6f9070b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testtttttttttttttttttttttttttttt (ID: 2f388ab9-8161-4f4e-8ffb-1a65afe5449a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadadawdad (ID: 66a1c1fb-be2a-41d6-a9b0-8f21e5efc08c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: bd36e320-c182-46a4-ba60-cc3ba4a0749c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadaw (ID: 1cb5ad85-59b9-4095-825f-1364ef6d291d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - diuashduihawuihduia (ID: 1306a6ad-3ea4-4106-b4ab-09d645770558)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsafasfs (ID: 0f5b2980-3acc-4024-b5b4-19450a419fdf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 62796a47-945e-4936-9a95-4fa450ed07d6)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdasad (ID: 16547467-d690-417e-bce9-4cd58a25c298)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdawaad (ID: 0c39f35c-a615-4550-ab9f-e295f8be70cb)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdadwawdwa (ID: f524878f-c8f3-4c03-9ca9-09f7a4a5d8c0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdaa (ID: a6007f69-3f95-4ecd-acde-d16d6ad62035)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sadsadwerwffsdfssssss (ID: af66689a-c04b-4249-a424-a710376856c5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwa (ID: 031efbcf-d63a-42ae-93f6-595fa590e61b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawaw (ID: f7b38e66-7063-48e5-b35a-48877a960372)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdawdaa (ID: a6dd2c36-80bb-4c61-8ea2-865ea919fabf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daasa (ID: 00000cbc-9402-4ef1-b2e3-ba097c34fdfa)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaaaaaaaaaa (ID: 0256ccaa-f6f6-478b-bb23-0ebce2fcb993)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadwadwd (ID: 59be9116-4344-48dc-a300-fd54b50cb51f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwaa (ID: 9b47b464-ef47-4123-8afe-b5da2a019d1b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaa (ID: 9e221a54-f864-4c17-9f6b-1d3b7f8350ad)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwada (ID: e1c23312-3859-40b2-ba18-624bd0d595c3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadwawda (ID: 8bef3ac5-a87e-40d5-9af4-9c545c5fa2fc)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fDAF (ID: 809bbcd3-c153-461e-a399-1ad841c4260c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - awdaw (ID: 42e9b17d-07df-4fba-bdd2-be676c80274a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdw (ID: aa2110b5-ddd6-4e2d-b17d-765dd51878c7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dsaaaaaaaaaaaa (ID: 2442bf9f-8632-4327-b68a-78aa57b420cf)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 47668211-a0c8-453a-8658-c35c8acf9cdd)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaa33333 (ID: b1ecfb16-e7c9-4919-b2b8-370550252426)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadaw (ID: aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wddawdwa (ID: 23aa3f82-2714-4521-8789-be2b00331013)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawda (ID: d84f573a-932a-4af7-9095-719939a07fa4)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwdaw (ID: 358a13f3-a1db-4741-8235-71300fd72993)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdada (ID: 75c433cf-7bcb-43eb-8d42-c782aa793f38)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdwad (ID: f69a3537-b316-401e-8412-303a459930be)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawadasd (ID: ae0e56ab-942d-43ab-8a30-7c16be4a98fe)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsdfdsdfa3343224324 (ID: 21638641-943d-4c4b-a075-f3abcc46797f)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 778bd3d6-bc14-459b-a87a-17869d9e9e04)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadasdasd (ID: 0dd7fa52-ce6c-4cf3-a6fb-1fd6edb77c36)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawdwad (ID: 2adf158d-d3bf-4038-8e49-f53541fa73e8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawd (ID: 253734e7-c9f5-4d35-bcd6-302e42fc0ea8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing (ID: 079df915-52f6-4e8c-b5c3-54d04e586c07)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dadawdadawd (ID: 732435c6-5e90-41c9-a5a4-207559f9bc51)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadawdawd (ID: 067f1e0a-5eef-42cd-981d-65a1ef04392b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadawwda (ID: 3793b743-654c-46d4-8b68-dcadb593ceda)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - aaaaaaaaaaaaaaaaaaawwwwwwwwwwwww (ID: 67641bc2-d6a5-400b-b469-50ff5df29aa9)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwaa (ID: 1b8c6372-517b-4aa5-bf37-e0b10c14897a)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawa (ID: 90939159-fabb-462d-9d92-3105cce322e1)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdw (ID: afa43070-a3ac-4134-841f-efc29f43c218)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sfdsfetdfcfvcbbfghfhfhfdgfgrgf (ID: de8544a5-e176-4038-b699-419018a1530d)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaw (ID: 0e4d4772-3d89-453c-ba71-2426e168ef65)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - ddddddddddddddddddddddddddddddd (ID: 01ace668-ce82-42b3-8f7c-c9a3219d4967)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - testing perm (ID: 25fd387c-35b2-43b1-9df4-95f0149c62c9)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sad (ID: 7331902b-ddbb-498e-b365-71d88ae42ca5)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - daw (ID: 39cf0b90-2e6a-49e4-a5fd-c5c078a62a9c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - oui (ID: f0806c7c-7cb3-4f6a-9447-5d782af01932)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdwadwa (ID: f85bd8b2-1374-424a-89b0-2625bc473bd8)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - sdadw (ID: 4a7dbb6e-8289-4274-9a11-25a61493c128)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawddaw (ID: a5d6a06b-a9c7-4067-be5e-a0209e201fa0)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwadawd (ID: 3dd94ea9-ae58-488a-bb8a-bc4342a4fd2b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdawdaaaaaaa (ID: 08db5308-e82e-4ee5-9f66-1f66a5da672b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdsadasdasd (ID: c2569049-fcdf-4278-ac80-d1cf35c645db)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdwdadw (ID: a06dce52-acae-4249-bc9b-eb3840ee3e7e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wadawdawddaw (ID: 9ff3db28-a0d5-45cd-9b59-fd34d9640724)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 03e91d98-f031-4f21-b5f8-bb413f2cbf1b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: 8a568eb9-0e15-4e8f-8185-96874fe6477e)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - fuk (ID: 7d84ee4d-45ff-4ec6-b50e-fd3f2d9e74ad)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - rrew (ID: 2f9a5448-417b-4f32-8023-44c71886c60c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawddwa (ID: d2fa02b5-eea0-495a-949c-d32c8e171be7)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - wdawdadw (ID: 612ff681-0fbe-42d1-b927-d4b9c12a5982)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - waddwaad (ID: 9ee99cde-9bb9-4d9d-83ee-df2342551b7c)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - asdadwadsa (ID: 116028e9-21cf-435b-bedd-3745d285c7fa)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dwadwdaw (ID: 31d13c67-f565-4bd0-9c4d-a9ab620a5913)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawwadwad (ID: f67d2e01-39e2-4cb0-8180-7dc6c1d78342)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - Unknown Town (ID: b51e7ecf-d238-48e7-8624-9540bf816a14)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dasdawdwa (ID: 2d685456-5807-4590-b4ec-2648a069959b)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) CLIENT:   - dawdad (ID: 220f69b7-1a72-4180-9533-1cb20000f234)
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received permission data for player: 681f539b-8bb8-3f85-85e5-a2945f6c6539 with 0 categories
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received permission data for player: 11e2b7aa-6bda-3ca8-b793-391234896312 with 0 categories
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received permission data for player: 34c6f761-a0d4-3d62-b5ee-df15e4530215 with 0 categories
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received permission data for player: aed5efd4-551b-3965-bc28-ae21aa072a66 with 0 categories
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received town invite packet
[15:17:27] [Netty Epoll Client IO #1/INFO] (pokecobbleclaim) Received invitation from town: wadaw (aceee40b-077b-421b-83ee-36e196d28fc3)
[15:17:27] [Render thread/INFO] (Minecraft) [System] [CHAT] You have joined wadaw
[15:17:27] [Render thread/INFO] (pokecobbleclaim) Clearing pending invite for town: wadaw
